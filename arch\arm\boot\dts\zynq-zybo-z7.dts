// SPDX-License-Identifier: GPL-2.0+
/dts-v1/;
#include "zynq-7000.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	model = "Digilent Zybo Z7 board";
	compatible = "digilent,zynq-zybo-z7", "xlnx,zynq-7000";

	aliases {
		ethernet0 = &gem0;
		serial0 = &uart1;
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};

	chosen {
		bootargs = "";
		stdout-path = "serial0:115200n8";
	};

	gpio-leds {
		compatible = "gpio-leds";

		ld4 {
			label = "zynq-zybo-z7:green:ld4";
			gpios = <&gpio0 7 GPIO_ACTIVE_HIGH>;
		};
	};

	usb_phy0: phy0 {
		#phy-cells = <0>;
		compatible = "usb-nop-xceiv";
		reset-gpios = <&gpio0 46 GPIO_ACTIVE_LOW>;
	};
};

&clkc {
	ps-clk-frequency = <33333333>;
};

&gem0 {
	status = "okay";
	phy-mode = "rgmii-id";
	phy-handle = <&ethernet_phy>;

	ethernet_phy: ethernet-phy@0 {
		reg = <0>;
		device_type = "ethernet-phy";
	};
};

&sdhci0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&usb0 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy0>;
};
