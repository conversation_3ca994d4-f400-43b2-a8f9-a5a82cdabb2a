// SPDX-License-Identifier: GPL-2.0-only
/*
 * Abilis Systems TB101 SOC device tree
 *
 * Copyright (C) Abilis Systems 2013
 *
 * Author: <PERSON> <<EMAIL>>
 */

/include/ "abilis_tb10x.dtsi"


/ {
	soc100 {
		bus-frequency	= <*********>;

		pll0: oscillator {
			clock-frequency  = <**********>;
		};
		cpu_clk: clkdiv_cpu {
			clock-mult = <1>;
			clock-div = <2>;
		};
		ahb_clk: clkdiv_ahb {
			clock-mult = <1>;
			clock-div = <6>;
		};

		iomux: iomux@ff10601c {
			/* Port 1 */
			pctl_tsin_s0: pctl-tsin-s0 {   /* Serial TS-in 0 */
				abilis,function = "mis0";
			};
			pctl_tsin_s1: pctl-tsin-s1 {   /* Serial TS-in 1 */
				abilis,function = "mis1";
			};
			pctl_gpio_a: pctl-gpio-a {     /* GPIO bank A */
				abilis,function = "gpioa";
			};
			pctl_tsin_p1: pctl-tsin-p1 {   /* Parallel TS-in 1 */
				abilis,function = "mip1";
			};
			/* Port 2 */
			pctl_tsin_s2: pctl-tsin-s2 {   /* Serial TS-in 2 */
				abilis,function = "mis2";
			};
			pctl_tsin_s3: pctl-tsin-s3 {   /* Serial TS-in 3 */
				abilis,function = "mis3";
			};
			pctl_gpio_c: pctl-gpio-c {     /* GPIO bank C */
				abilis,function = "gpioc";
			};
			pctl_tsin_p3: pctl-tsin-p3 {   /* Parallel TS-in 3 */
				abilis,function = "mip3";
			};
			/* Port 3 */
			pctl_tsin_s4: pctl-tsin-s4 {   /* Serial TS-in 4 */
				abilis,function = "mis4";
			};
			pctl_tsin_s5: pctl-tsin-s5 {   /* Serial TS-in 5 */
				abilis,function = "mis5";
			};
			pctl_gpio_e: pctl-gpio-e {     /* GPIO bank E */
				abilis,function = "gpioe";
			};
			pctl_tsin_p5: pctl-tsin-p5 {   /* Parallel TS-in 5 */
				abilis,function = "mip5";
			};
			/* Port 4 */
			pctl_tsin_s6: pctl-tsin-s6 {   /* Serial TS-in 6 */
				abilis,function = "mis6";
			};
			pctl_tsin_s7: pctl-tsin-s7 {   /* Serial TS-in 7 */
				abilis,function = "mis7";
			};
			pctl_gpio_g: pctl-gpio-g {     /* GPIO bank G */
				abilis,function = "gpiog";
			};
			pctl_tsin_p7: pctl-tsin-p7 {   /* Parallel TS-in 7 */
				abilis,function = "mip7";
			};
			/* Port 5 */
			pctl_gpio_j: pctl-gpio-j {     /* GPIO bank J */
				abilis,function = "gpioj";
			};
			pctl_gpio_k: pctl-gpio-k {     /* GPIO bank K */
				abilis,function = "gpiok";
			};
			pctl_ciplus: pctl-ciplus {     /* CI+ interface */
				abilis,function = "ciplus";
			};
			pctl_mcard: pctl-mcard {       /* M-Card interface */
				abilis,function = "mcard";
			};
			pctl_stc0: pctl-stc0 {         /* Smart card I/F 0 */
				abilis,function = "stc0";
			};
			pctl_stc1: pctl-stc1 {         /* Smart card I/F 1 */
				abilis,function = "stc1";
			};
			/* Port 6 */
			pctl_tsout_p: pctl-tsout-p {   /* Parallel TS-out */
				abilis,function = "mop";
			};
			pctl_tsout_s0: pctl-tsout-s0 { /* Serial TS-out 0 */
				abilis,function = "mos0";
			};
			pctl_tsout_s1: pctl-tsout-s1 { /* Serial TS-out 1 */
				abilis,function = "mos1";
			};
			pctl_tsout_s2: pctl-tsout-s2 { /* Serial TS-out 2 */
				abilis,function = "mos2";
			};
			pctl_tsout_s3: pctl-tsout-s3 { /* Serial TS-out 3 */
				abilis,function = "mos3";
			};
			/* Port 7 */
			pctl_uart0: pctl-uart0 {       /* UART 0 */
				abilis,function = "uart0";
			};
			pctl_uart1: pctl-uart1 {       /* UART 1 */
				abilis,function = "uart1";
			};
			pctl_gpio_l: pctl-gpio-l {     /* GPIO bank L */
				abilis,function = "gpiol";
			};
			pctl_gpio_m: pctl-gpio-m {     /* GPIO bank M */
				abilis,function = "gpiom";
			};
			/* Port 8 */
			pctl_spi3: pctl-spi3 {
				abilis,function = "spi3";
			};
			pctl_jtag: pctl-jtag {
				abilis,function = "jtag";
			};
			/* Port 9 */
			pctl_spi1: pctl-spi1 {
				abilis,function = "spi1";
			};
			pctl_gpio_n: pctl-gpio-n {
				abilis,function = "gpion";
			};
			/* Unmuxed GPIOs */
			pctl_gpio_b: pctl-gpio-b {
				abilis,function = "gpiob";
			};
			pctl_gpio_d: pctl-gpio-d {
				abilis,function = "gpiod";
			};
			pctl_gpio_f: pctl-gpio-f {
				abilis,function = "gpiof";
			};
			pctl_gpio_h: pctl-gpio-h {
				abilis,function = "gpioh";
			};
			pctl_gpio_i: pctl-gpio-i {
				abilis,function = "gpioi";
			};
		};

		gpioa: gpio@ff140000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff140000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <3>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpioa";
		};
		gpiob: gpio@ff141000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff141000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <2>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiob";
		};
		gpioc: gpio@ff142000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff142000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <3>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpioc";
		};
		gpiod: gpio@ff143000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff143000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <2>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiod";
		};
		gpioe: gpio@ff144000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff144000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <3>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpioe";
		};
		gpiof: gpio@ff145000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff145000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <2>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiof";
		};
		gpiog: gpio@ff146000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff146000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <3>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiog";
		};
		gpioh: gpio@ff147000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff147000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <2>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpioh";
		};
		gpioi: gpio@ff148000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff148000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <12>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpioi";
		};
		gpioj: gpio@ff149000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff149000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <32>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpioj";
		};
		gpiok: gpio@ff14a000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff14a000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <22>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiok";
		};
		gpiol: gpio@ff14b000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff14b000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <4>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiol";
		};
		gpiom: gpio@ff14c000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff14c000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <4>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpiom";
		};
		gpion: gpio@ff14d000 {
			compatible = "abilis,tb10x-gpio";
			interrupt-controller;
			#interrupt-cells = <1>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <27 2>;
			reg = <0xff14d000 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			abilis,ngpio = <5>;
			gpio-ranges = <&iomux 0 0 0>;
			gpio-ranges-group-names = "gpion";
		};
	};
};
