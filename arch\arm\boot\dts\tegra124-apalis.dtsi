// SPDX-License-Identifier: GPL-2.0 OR X11
/*
 * Copyright 2016-2019 Toradex AG
 */

#include "tegra124.dtsi"
#include "tegra124-apalis-emc.dtsi"

/*
 * Toradex Apalis TK1 Module Device Tree
 * Compatible for Revisions 2GB: V1.0A, V1.0B, V1.1A
 */
/ {
	memory@80000000 {
		reg = <0x0 0x80000000 0x0 0x80000000>;
	};

	pcie@1003000 {
		status = "okay";
		avddio-pex-supply = <&reg_1v05_vdd>;
		avdd-pex-pll-supply = <&reg_1v05_vdd>;
		avdd-pll-erefe-supply = <&reg_1v05_avdd>;
		dvddio-pex-supply = <&reg_1v05_vdd>;
		hvdd-pex-pll-e-supply = <&reg_module_3v3>;
		hvdd-pex-supply = <&reg_module_3v3>;
		vddio-pex-ctl-supply = <&reg_module_3v3>;

		/* Apalis PCIe (additional lane Apalis type specific) */
		pci@1,0 {
			/* PCIE1_RX/TX and TS_DIFF1/2 */
			phys = <&{/padctl@7009f000/pads/pcie/lanes/pcie-4}>,
			       <&{/padctl@7009f000/pads/pcie/lanes/pcie-3}>;
			phy-names = "pcie-0", "pcie-1";
		};

		/* I210 Gigabit Ethernet Controller (On-module) */
		pci@2,0 {
			phys = <&{/padctl@7009f000/pads/pcie/lanes/pcie-2}>;
			phy-names = "pcie-0";
			status = "okay";

			pcie@0 {
				reg = <0 0 0 0 0>;
				local-mac-address = [00 00 00 00 00 00];
			};
		};
	};

	host1x@50000000 {
		hdmi@54280000 {
			nvidia,ddc-i2c-bus = <&hdmi_ddc>;
			nvidia,hpd-gpio =
				<&gpio TEGRA_GPIO(N, 7) GPIO_ACTIVE_HIGH>;
			pll-supply = <&reg_1v05_avdd_hdmi_pll>;
			vdd-supply = <&reg_3v3_avdd_hdmi>;
		};
	};

	gpu@0,57000000 {
		/*
		 * Node left disabled on purpose - the bootloader will enable
		 * it after having set the VPR up
		 */
		vdd-supply = <&reg_vdd_gpu>;
	};

	pinmux@70000868 {
		pinctrl-names = "default";
		pinctrl-0 = <&state_default>;

		state_default: pinmux {
			/* Analogue Audio (On-module) */
			dap3-fs-pp0 {
				nvidia,pins = "dap3_fs_pp0";
				nvidia,function = "i2s2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap3-din-pp1 {
				nvidia,pins = "dap3_din_pp1";
				nvidia,function = "i2s2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			dap3-dout-pp2 {
				nvidia,pins = "dap3_dout_pp2";
				nvidia,function = "i2s2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap3-sclk-pp3 {
				nvidia,pins = "dap3_sclk_pp3";
				nvidia,function = "i2s2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap-mclk1-pw4 {
				nvidia,pins = "dap_mclk1_pw4";
				nvidia,function = "extperiph1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis BKL1_ON */
			pbb5 {
				nvidia,pins = "pbb5";
				nvidia,function = "vgp5";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis BKL1_PWM */
			pu6 {
				nvidia,pins = "pu6";
				nvidia,function = "pwm3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis CAM1_MCLK */
			cam-mclk-pcc0 {
				nvidia,pins = "cam_mclk_pcc0";
				nvidia,function = "vi_alt3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis Digital Audio */
			dap2-fs-pa2 {
				nvidia,pins = "dap2_fs_pa2";
				nvidia,function = "hda";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			dap2-sclk-pa3 {
				nvidia,pins = "dap2_sclk_pa3";
				nvidia,function = "hda";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			dap2-din-pa4 {
				nvidia,pins = "dap2_din_pa4";
				nvidia,function = "hda";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			dap2-dout-pa5 {
				nvidia,pins = "dap2_dout_pa5";
				nvidia,function = "hda";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pbb3 { /* DAP1_RESET */
				nvidia,pins = "pbb3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			clk3-out-pee0 {
				nvidia,pins = "clk3_out_pee0";
				nvidia,function = "extperiph3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis GPIO */
			ddc-scl-pv4 {
				nvidia,pins = "ddc_scl_pv4";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			ddc-sda-pv5 {
				nvidia,pins = "ddc_sda_pv5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pex-l0-rst-n-pdd1 {
				nvidia,pins = "pex_l0_rst_n_pdd1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pex-l0-clkreq-n-pdd2 {
				nvidia,pins = "pex_l0_clkreq_n_pdd2";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pex-l1-rst-n-pdd5 {
				nvidia,pins = "pex_l1_rst_n_pdd5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pex-l1-clkreq-n-pdd6 {
				nvidia,pins = "pex_l1_clkreq_n_pdd6";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			dp-hpd-pff0 {
				nvidia,pins = "dp_hpd_pff0";
				nvidia,function = "dp";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pff2 {
				nvidia,pins = "pff2";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			owr { /* PEX_L1_CLKREQ_N multiplexed GPIO6 */
				nvidia,pins = "owr";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				nvidia,rcv-sel = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis HDMI1_CEC */
			hdmi-cec-pee3 {
				nvidia,pins = "hdmi_cec_pee3";
				nvidia,function = "cec";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis HDMI1_HPD */
			hdmi-int-pn7 {
				nvidia,pins = "hdmi_int_pn7";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,rcv-sel = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis I2C1 */
			gen1-i2c-scl-pc4 {
				nvidia,pins = "gen1_i2c_scl_pc4";
				nvidia,function = "i2c1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};
			gen1-i2c-sda-pc5 {
				nvidia,pins = "gen1_i2c_sda_pc5";
				nvidia,function = "i2c1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis I2C2 (DDC) */
			gen2-i2c-scl-pt5 {
				nvidia,pins = "gen2_i2c_scl_pt5";
				nvidia,function = "i2c2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};
			gen2-i2c-sda-pt6 {
				nvidia,pins = "gen2_i2c_sda_pt6";
				nvidia,function = "i2c2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis I2C3 (CAM) */
			cam-i2c-scl-pbb1 {
				nvidia,pins = "cam_i2c_scl_pbb1";
				nvidia,function = "i2c3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};
			cam-i2c-sda-pbb2 {
				nvidia,pins = "cam_i2c_sda_pbb2";
				nvidia,function = "i2c3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis MMC1 */
			sdmmc1-cd-n-pv3 { /* CD# GPIO */
				nvidia,pins = "sdmmc1_wp_n_pv3";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			clk2-out-pw5 { /* D5 GPIO */
				nvidia,pins = "clk2_out_pw5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc1-dat3-py4 {
				nvidia,pins = "sdmmc1_dat3_py4";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc1-dat2-py5 {
				nvidia,pins = "sdmmc1_dat2_py5";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc1-dat1-py6 {
				nvidia,pins = "sdmmc1_dat1_py6";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc1-dat0-py7 {
				nvidia,pins = "sdmmc1_dat0_py7";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc1-clk-pz0 {
				nvidia,pins = "sdmmc1_clk_pz0";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc1-cmd-pz1 {
				nvidia,pins = "sdmmc1_cmd_pz1";
				nvidia,function = "sdmmc1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			clk2-req-pcc5 { /* D4 GPIO */
				nvidia,pins = "clk2_req_pcc5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-clk-lb-in-pee5 { /* D6 GPIO */
				nvidia,pins = "sdmmc3_clk_lb_in_pee5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			usb-vbus-en2-pff1 { /* D7 GPIO */
				nvidia,pins = "usb_vbus_en2_pff1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis PWM */
			ph0 {
				nvidia,pins = "ph0";
				nvidia,function = "pwm0";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ph1 {
				nvidia,pins = "ph1";
				nvidia,function = "pwm1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ph2 {
				nvidia,pins = "ph2";
				nvidia,function = "pwm2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			/* PWM3 active on pu6 being Apalis BKL1_PWM as well */
			ph3 {
				nvidia,pins = "ph3";
				nvidia,function = "pwm3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis SATA1_ACT# */
			dap1-dout-pn2 {
				nvidia,pins = "dap1_dout_pn2";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis SD1 */
			sdmmc3-clk-pa6 {
				nvidia,pins = "sdmmc3_clk_pa6";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-cmd-pa7 {
				nvidia,pins = "sdmmc3_cmd_pa7";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-dat3-pb4 {
				nvidia,pins = "sdmmc3_dat3_pb4";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-dat2-pb5 {
				nvidia,pins = "sdmmc3_dat2_pb5";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-dat1-pb6 {
				nvidia,pins = "sdmmc3_dat1_pb6";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-dat0-pb7 {
				nvidia,pins = "sdmmc3_dat0_pb7";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc3-cd-n-pv2 { /* CD# GPIO */
				nvidia,pins = "sdmmc3_cd_n_pv2";
				nvidia,function = "rsvd3";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis SPDIF */
			spdif-out-pk5 {
				nvidia,pins = "spdif_out_pk5";
				nvidia,function = "spdif";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			spdif-in-pk6 {
				nvidia,pins = "spdif_in_pk6";
				nvidia,function = "spdif";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis SPI1 */
			ulpi-clk-py0 {
				nvidia,pins = "ulpi_clk_py0";
				nvidia,function = "spi1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-dir-py1 {
				nvidia,pins = "ulpi_dir_py1";
				nvidia,function = "spi1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			ulpi-nxt-py2 {
				nvidia,pins = "ulpi_nxt_py2";
				nvidia,function = "spi1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-stp-py3 {
				nvidia,pins = "ulpi_stp_py3";
				nvidia,function = "spi1";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis SPI2 */
			pg5 {
				nvidia,pins = "pg5";
				nvidia,function = "spi4";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg6 {
				nvidia,pins = "pg6";
				nvidia,function = "spi4";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg7 {
				nvidia,pins = "pg7";
				nvidia,function = "spi4";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pi3 {
				nvidia,pins = "pi3";
				nvidia,function = "spi4";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis UART1 */
			pb1 { /* DCD GPIO */
				nvidia,pins = "pb1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			pk7 { /* RI GPIO */
				nvidia,pins = "pk7";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart1-txd-pu0 {
				nvidia,pins = "pu0";
				nvidia,function = "uarta";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			uart1-rxd-pu1 {
				nvidia,pins = "pu1";
				nvidia,function = "uarta";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart1-cts-n-pu2 {
				nvidia,pins = "pu2";
				nvidia,function = "uarta";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart1-rts-n-pu3 {
				nvidia,pins = "pu3";
				nvidia,function = "uarta";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			uart3-cts-n-pa1 { /* DSR GPIO */
				nvidia,pins = "uart3_cts_n_pa1";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart3-rts-n-pc0 { /* DTR GPIO */
				nvidia,pins = "uart3_rts_n_pc0";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis UART2 */
			uart2-txd-pc2 {
				nvidia,pins = "uart2_txd_pc2";
				nvidia,function = "irda";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			uart2-rxd-pc3 {
				nvidia,pins = "uart2_rxd_pc3";
				nvidia,function = "irda";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart2-cts-n-pj5 {
				nvidia,pins = "uart2_cts_n_pj5";
				nvidia,function = "uartb";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart2-rts-n-pj6 {
				nvidia,pins = "uart2_rts_n_pj6";
				nvidia,function = "uartb";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis UART3 */
			uart3-txd-pw6 {
				nvidia,pins = "uart3_txd_pw6";
				nvidia,function = "uartc";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			uart3-rxd-pw7 {
				nvidia,pins = "uart3_rxd_pw7";
				nvidia,function = "uartc";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis UART4 */
			uart4-rxd-pb0 {
				nvidia,pins = "pb0";
				nvidia,function = "uartd";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			uart4-txd-pj7 {
				nvidia,pins = "pj7";
				nvidia,function = "uartd";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis USBH_EN */
			usb-vbus-en1-pn5 {
				nvidia,pins = "usb_vbus_en1_pn5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				nvidia,open-drain = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis USBH_OC# */
			pbb0 {
				nvidia,pins = "pbb0";
				nvidia,function = "vgp6";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis USBO1_EN */
			usb-vbus-en0-pn4 {
				nvidia,pins = "usb_vbus_en0_pn4";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
				nvidia,open-drain = <TEGRA_PIN_DISABLE>;
			};

			/* Apalis USBO1_OC# */
			pbb4 {
				nvidia,pins = "pbb4";
				nvidia,function = "vgp4";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* Apalis WAKE1_MICO */
			pex-wake-n-pdd3 {
				nvidia,pins = "pex_wake_n_pdd3";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* CORE_PWR_REQ */
			core-pwr-req {
				nvidia,pins = "core_pwr_req";
				nvidia,function = "pwron";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* CPU_PWR_REQ */
			cpu-pwr-req {
				nvidia,pins = "cpu_pwr_req";
				nvidia,function = "cpu";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* DVFS */
			dvfs-pwm-px0 {
				nvidia,pins = "dvfs_pwm_px0";
				nvidia,function = "cldvfs";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dvfs-clk-px2 {
				nvidia,pins = "dvfs_clk_px2";
				nvidia,function = "cldvfs";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* eMMC */
			sdmmc4-dat0-paa0 {
				nvidia,pins = "sdmmc4_dat0_paa0";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat1-paa1 {
				nvidia,pins = "sdmmc4_dat1_paa1";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat2-paa2 {
				nvidia,pins = "sdmmc4_dat2_paa2";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat3-paa3 {
				nvidia,pins = "sdmmc4_dat3_paa3";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat4-paa4 {
				nvidia,pins = "sdmmc4_dat4_paa4";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat5-paa5 {
				nvidia,pins = "sdmmc4_dat5_paa5";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat6-paa6 {
				nvidia,pins = "sdmmc4_dat6_paa6";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-dat7-paa7 {
				nvidia,pins = "sdmmc4_dat7_paa7";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-clk-pcc4 {
				nvidia,pins = "sdmmc4_clk_pcc4";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			sdmmc4-cmd-pt7 {
				nvidia,pins = "sdmmc4_cmd_pt7";
				nvidia,function = "sdmmc4";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* JTAG_RTCK */
			jtag-rtck {
				nvidia,pins = "jtag_rtck";
				nvidia,function = "rtck";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* LAN_DEV_OFF# */
			ulpi-data5-po6 {
				nvidia,pins = "ulpi_data5_po6";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* LAN_RESET# */
			kb-row10-ps2 {
				nvidia,pins = "kb_row10_ps2";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* LAN_WAKE# */
			ulpi-data4-po5 {
				nvidia,pins = "ulpi_data4_po5";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* MCU_INT1# */
			pk2 {
				nvidia,pins = "pk2";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* MCU_INT2# */
			pj2 {
				nvidia,pins = "pj2";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* MCU_INT3# */
			pi5 {
				nvidia,pins = "pi5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* MCU_INT4# */
			pj0 {
				nvidia,pins = "pj0";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* MCU_RESET */
			pbb6 {
				nvidia,pins = "pbb6";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* MCU SPI */
			gpio-x4-aud-px4 {
				nvidia,pins = "gpio_x4_aud_px4";
				nvidia,function = "spi2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			gpio-x5-aud-px5 {
				nvidia,pins = "gpio_x5_aud_px5";
				nvidia,function = "spi2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			gpio-x6-aud-px6 { /* MCU_CS */
				nvidia,pins = "gpio_x6_aud_px6";
				nvidia,function = "spi2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			gpio-x7-aud-px7 {
				nvidia,pins = "gpio_x7_aud_px7";
				nvidia,function = "spi2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
			gpio-w2-aud-pw2 { /* MCU_CSEZP */
				nvidia,pins = "gpio_w2_aud_pw2";
				nvidia,function = "spi2";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* PMIC_CLK_32K */
			clk-32k-in {
				nvidia,pins = "clk_32k_in";
				nvidia,function = "clk";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* PMIC_CPU_OC_INT */
			clk-32k-out-pa0 {
				nvidia,pins = "clk_32k_out_pa0";
				nvidia,function = "soc";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* PWR_I2C */
			pwr-i2c-scl-pz6 {
				nvidia,pins = "pwr_i2c_scl_pz6";
				nvidia,function = "i2cpwr";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};
			pwr-i2c-sda-pz7 {
				nvidia,pins = "pwr_i2c_sda_pz7";
				nvidia,function = "i2cpwr";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
				nvidia,open-drain = <TEGRA_PIN_ENABLE>;
			};

			/* PWR_INT_N */
			pwr-int-n {
				nvidia,pins = "pwr_int_n";
				nvidia,function = "pmi";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* RESET_MOCI_CTRL */
			pu4 {
				nvidia,pins = "pu4";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* RESET_OUT_N */
			reset-out-n {
				nvidia,pins = "reset_out_n";
				nvidia,function = "reset_out_n";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* SHIFT_CTRL_DIR_IN */
			kb-row0-pr0 {
				nvidia,pins = "kb_row0_pr0";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row1-pr1 {
				nvidia,pins = "kb_row1_pr1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* Configure level-shifter as output for HDA */
			kb-row11-ps3 {
				nvidia,pins = "kb_row11_ps3";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* SHIFT_CTRL_DIR_OUT */
			kb-col5-pq5 {
				nvidia,pins = "kb_col5_pq5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-col6-pq6 {
				nvidia,pins = "kb_col6_pq6";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-col7-pq7 {
				nvidia,pins = "kb_col7_pq7";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* SHIFT_CTRL_OE */
			kb-col0-pq0 {
				nvidia,pins = "kb_col0_pq0";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-col1-pq1 {
				nvidia,pins = "kb_col1_pq1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-col2-pq2 {
				nvidia,pins = "kb_col2_pq2";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-col4-pq4 {
				nvidia,pins = "kb_col4_pq4";
				nvidia,function = "kbc";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row2-pr2 {
				nvidia,pins = "kb_row2_pr2";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};

			/* GPIO_PI6 aka TMP451 ALERT#/THERM2# */
			pi6 {
				nvidia,pins = "pi6";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_UP>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			/* TOUCH_INT */
			gpio-w3-aud-pw3 {
				nvidia,pins = "gpio_w3_aud_pw3";
				nvidia,function = "spi6";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};

			pc7 { /* NC */
				nvidia,pins = "pc7";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg0 { /* NC */
				nvidia,pins = "pg0";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg1 { /* NC */
				nvidia,pins = "pg1";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg2 { /* NC */
				nvidia,pins = "pg2";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg3 { /* NC */
				nvidia,pins = "pg3";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pg4 { /* NC */
				nvidia,pins = "pg4";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ph4 { /* NC */
				nvidia,pins = "ph4";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ph5 { /* NC */
				nvidia,pins = "ph5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ph6 { /* NC */
				nvidia,pins = "ph6";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ph7 { /* NC */
				nvidia,pins = "ph7";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pi0 { /* NC */
				nvidia,pins = "pi0";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pi1 { /* NC */
				nvidia,pins = "pi1";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pi2 { /* NC */
				nvidia,pins = "pi2";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pi4 { /* NC */
				nvidia,pins = "pi4";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pi7 { /* NC */
				nvidia,pins = "pi7";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pk0 { /* NC */
				nvidia,pins = "pk0";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pk1 { /* NC */
				nvidia,pins = "pk1";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pk3 { /* NC */
				nvidia,pins = "pk3";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pk4 { /* NC */
				nvidia,pins = "pk4";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap1-fs-pn0 { /* NC */
				nvidia,pins = "dap1_fs_pn0";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap1-din-pn1 { /* NC */
				nvidia,pins = "dap1_din_pn1";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap1-sclk-pn3 { /* NC */
				nvidia,pins = "dap1_sclk_pn3";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-data7-po0 { /* NC */
				nvidia,pins = "ulpi_data7_po0";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-data0-po1 { /* NC */
				nvidia,pins = "ulpi_data0_po1";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-data1-po2 { /* NC */
				nvidia,pins = "ulpi_data1_po2";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-data2-po3 { /* NC */
				nvidia,pins = "ulpi_data2_po3";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-data3-po4 { /* NC */
				nvidia,pins = "ulpi_data3_po4";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			ulpi-data6-po7 { /* NC */
				nvidia,pins = "ulpi_data6_po7";
				nvidia,function = "ulpi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap4-fs-pp4 { /* NC */
				nvidia,pins = "dap4_fs_pp4";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap4-din-pp5 { /* NC */
				nvidia,pins = "dap4_din_pp5";
				nvidia,function = "rsvd3";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap4-dout-pp6 { /* NC */
				nvidia,pins = "dap4_dout_pp6";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap4-sclk-pp7 { /* NC */
				nvidia,pins = "dap4_sclk_pp7";
				nvidia,function = "rsvd3";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-col3-pq3 { /* NC */
				nvidia,pins = "kb_col3_pq3";
				nvidia,function = "kbc";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row3-pr3 { /* NC */
				nvidia,pins = "kb_row3_pr3";
				nvidia,function = "kbc";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row4-pr4 { /* NC */
				nvidia,pins = "kb_row4_pr4";
				nvidia,function = "rsvd3";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row5-pr5 { /* NC */
				nvidia,pins = "kb_row5_pr5";
				nvidia,function = "rsvd3";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row6-pr6 { /* NC */
				nvidia,pins = "kb_row6_pr6";
				nvidia,function = "kbc";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row7-pr7 { /* NC */
				nvidia,pins = "kb_row7_pr7";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row8-ps0 { /* NC */
				nvidia,pins = "kb_row8_ps0";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row9-ps1 { /* NC */
				nvidia,pins = "kb_row9_ps1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row12-ps4 { /* NC */
				nvidia,pins = "kb_row12_ps4";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row13-ps5 { /* NC */
				nvidia,pins = "kb_row13_ps5";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row14-ps6 { /* NC */
				nvidia,pins = "kb_row14_ps6";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row15-ps7 { /* NC */
				nvidia,pins = "kb_row15_ps7";
				nvidia,function = "rsvd3";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row16-pt0 { /* NC */
				nvidia,pins = "kb_row16_pt0";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			kb-row17-pt1 { /* NC */
				nvidia,pins = "kb_row17_pt1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pu5 { /* NC */
				nvidia,pins = "pu5";
				nvidia,function = "gmi";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pv0 { /* NC */
				nvidia,pins = "pv0";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pv1 { /* NC */
				nvidia,pins = "pv1";
				nvidia,function = "rsvd1";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			gpio-x1-aud-px1 { /* NC */
				nvidia,pins = "gpio_x1_aud_px1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			gpio-x3-aud-px3 { /* NC */
				nvidia,pins = "gpio_x3_aud_px3";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pbb7 { /* NC */
				nvidia,pins = "pbb7";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pcc1 { /* NC */
				nvidia,pins = "pcc1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			pcc2 { /* NC */
				nvidia,pins = "pcc2";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			clk3-req-pee1 { /* NC */
				nvidia,pins = "clk3_req_pee1";
				nvidia,function = "rsvd2";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			dap-mclk1-req-pee2 { /* NC */
				nvidia,pins = "dap_mclk1_req_pee2";
				nvidia,function = "rsvd4";
				nvidia,pull = <TEGRA_PIN_PULL_DOWN>;
				nvidia,tristate = <TEGRA_PIN_ENABLE>;
				nvidia,enable-input = <TEGRA_PIN_DISABLE>;
			};
			/*
			 * Leave SDMMC3_CLK_LB_OUT muxed as SDMMC3 with output
			 * driver enabled aka not tristated and input driver
			 * enabled as well as it features some magic properties
			 * even though the external loopback is disabled and the
			 * internal loopback used as per
			 * SDMMC_VENDOR_MISC_CNTRL_0 register's SDMMC_SPARE1
			 * bits being set to 0xfffd according to the TRM!
			 */
			sdmmc3-clk-lb-out-pee4 { /* NC */
				nvidia,pins = "sdmmc3_clk_lb_out_pee4";
				nvidia,function = "sdmmc3";
				nvidia,pull = <TEGRA_PIN_PULL_NONE>;
				nvidia,tristate = <TEGRA_PIN_DISABLE>;
				nvidia,enable-input = <TEGRA_PIN_ENABLE>;
			};
		};
	};

	serial@70006040 {
		compatible = "nvidia,tegra124-hsuart", "nvidia,tegra30-hsuart";
	};

	serial@70006200 {
		compatible = "nvidia,tegra124-hsuart", "nvidia,tegra30-hsuart";
	};

	serial@70006300 {
		compatible = "nvidia,tegra124-hsuart", "nvidia,tegra30-hsuart";
	};

	hdmi_ddc: i2c@7000c400 {
		clock-frequency = <10000>;
	};

	/* PWR_I2C: power I2C to audio codec, PMIC and temperature sensor */
	i2c@7000d000 {
		status = "okay";
		clock-frequency = <400000>;

		/* SGTL5000 audio codec */
		sgtl5000: codec@a {
			compatible = "fsl,sgtl5000";
			reg = <0x0a>;
			VDDA-supply = <&reg_module_3v3_audio>;
			VDDD-supply = <&reg_1v8_vddio>;
			VDDIO-supply = <&reg_1v8_vddio>;
			clocks = <&tegra_car TEGRA124_CLK_EXTERN1>;
		};

		pmic: pmic@40 {
			compatible = "ams,as3722";
			reg = <0x40>;
			interrupts = <0 86 IRQ_TYPE_LEVEL_HIGH>;
			ams,system-power-controller;
			#interrupt-cells = <2>;
			interrupt-controller;
			gpio-controller;
			#gpio-cells = <2>;
			pinctrl-names = "default";
			pinctrl-0 = <&as3722_default>;

			as3722_default: pinmux {
				gpio2-7 {
					pins = "gpio2", /* PWR_EN_+V3.3 */
					       "gpio7"; /* +V1.6_LPO */
					function = "gpio";
					bias-pull-up;
				};

				gpio0-1-3-4-5-6 {
					pins = "gpio0", "gpio1", "gpio3",
					       "gpio4", "gpio5", "gpio6";
					bias-high-impedance;
				};
			};

			regulators {
				vsup-sd2-supply = <&reg_module_3v3>;
				vsup-sd3-supply = <&reg_module_3v3>;
				vsup-sd4-supply = <&reg_module_3v3>;
				vsup-sd5-supply = <&reg_module_3v3>;
				vin-ldo0-supply = <&reg_1v35_vddio_ddr>;
				vin-ldo1-6-supply = <&reg_module_3v3>;
				vin-ldo2-5-7-supply = <&reg_1v8_vddio>;
				vin-ldo3-4-supply = <&reg_module_3v3>;
				vin-ldo9-10-supply = <&reg_module_3v3>;
				vin-ldo11-supply = <&reg_module_3v3>;

				reg_vdd_cpu: sd0 {
					regulator-name = "+VDD_CPU_AP";
					regulator-min-microvolt = <700000>;
					regulator-max-microvolt = <1400000>;
					regulator-min-microamp = <3500000>;
					regulator-max-microamp = <3500000>;
					regulator-always-on;
					regulator-boot-on;
					ams,ext-control = <2>;
				};

				sd1 {
					regulator-name = "+VDD_CORE";
					regulator-min-microvolt = <700000>;
					regulator-max-microvolt = <1350000>;
					regulator-min-microamp = <2500000>;
					regulator-max-microamp = <4000000>;
					regulator-always-on;
					regulator-boot-on;
					ams,ext-control = <1>;
				};

				reg_1v35_vddio_ddr: sd2 {
					regulator-name =
						"+V1.35_VDDIO_DDR(sd2)";
					regulator-min-microvolt = <1350000>;
					regulator-max-microvolt = <1350000>;
					regulator-always-on;
					regulator-boot-on;
				};

				sd3 {
					regulator-name =
						"+V1.35_VDDIO_DDR(sd3)";
					regulator-min-microvolt = <1350000>;
					regulator-max-microvolt = <1350000>;
					regulator-always-on;
					regulator-boot-on;
				};

				reg_1v05_vdd: sd4 {
					regulator-name = "+V1.05";
					regulator-min-microvolt = <1050000>;
					regulator-max-microvolt = <1050000>;
				};

				reg_1v8_vddio: sd5 {
					regulator-name = "+V1.8";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
					regulator-boot-on;
					regulator-always-on;
				};

				reg_vdd_gpu: sd6 {
					regulator-name = "+VDD_GPU_AP";
					regulator-min-microvolt = <650000>;
					regulator-max-microvolt = <1200000>;
					regulator-min-microamp = <3500000>;
					regulator-max-microamp = <3500000>;
					regulator-boot-on;
					regulator-always-on;
				};

				reg_1v05_avdd: ldo0 {
					regulator-name = "+V1.05_AVDD";
					regulator-min-microvolt = <1050000>;
					regulator-max-microvolt = <1050000>;
					regulator-boot-on;
					regulator-always-on;
					ams,ext-control = <1>;
				};

				vddio_sdmmc1: ldo1 {
					regulator-name = "VDDIO_SDMMC1";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <3300000>;
				};

				ldo2 {
					regulator-name = "+V1.2";
					regulator-min-microvolt = <1200000>;
					regulator-max-microvolt = <1200000>;
					regulator-boot-on;
					regulator-always-on;
				};

				ldo3 {
					regulator-name = "+V1.05_RTC";
					regulator-min-microvolt = <1000000>;
					regulator-max-microvolt = <1000000>;
					regulator-boot-on;
					regulator-always-on;
					ams,enable-tracking;
				};

				/* 1.8V for LVDS, 3.3V for eDP */
				ldo4 {
					regulator-name = "AVDD_LVDS0_PLL";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
				};

				/* LDO5 not used */

				vddio_sdmmc3: ldo6 {
					regulator-name = "VDDIO_SDMMC3";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <3300000>;
				};

				/* LDO7 not used */

				ldo9 {
					regulator-name = "+V3.3_ETH(ldo9)";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					regulator-always-on;
				};

				ldo10 {
					regulator-name = "+V3.3_ETH(ldo10)";
					regulator-min-microvolt = <3300000>;
					regulator-max-microvolt = <3300000>;
					regulator-always-on;
				};

				ldo11 {
					regulator-name = "+V1.8_VPP_FUSE";
					regulator-min-microvolt = <1800000>;
					regulator-max-microvolt = <1800000>;
				};
			};
		};

		/*
		 * TMP451 temperature sensor
		 * Note: THERM_N directly connected to AS3722 PMIC THERM
		 */
		temp-sensor@4c {
			compatible = "ti,tmp451";
			reg = <0x4c>;
			interrupt-parent = <&gpio>;
			interrupts = <TEGRA_GPIO(I, 6) IRQ_TYPE_LEVEL_LOW>;
			#thermal-sensor-cells = <1>;
			vcc-supply = <&reg_module_3v3>;
		};
	};

	/* SPI2: MCU SPI */
	spi@7000d600 {
		status = "okay";
		spi-max-frequency = <25000000>;
	};

	pmc@7000e400 {
		nvidia,invert-interrupt;
		nvidia,suspend-mode = <1>;
		nvidia,cpu-pwr-good-time = <500>;
		nvidia,cpu-pwr-off-time = <300>;
		nvidia,core-pwr-good-time = <641 3845>;
		nvidia,core-pwr-off-time = <61036>;
		nvidia,core-power-req-active-high;
		nvidia,sys-clock-req-active-high;

		/* Set power_off bit in ResetControl register of AS3722 PMIC */
		i2c-thermtrip {
			nvidia,i2c-controller-id = <4>;
			nvidia,bus-addr = <0x40>;
			nvidia,reg-addr = <0x36>;
			nvidia,reg-data = <0x2>;
		};
	};

	sata@70020000 {
		phys = <&{/padctl@7009f000/pads/sata/lanes/sata-0}>;
		phy-names = "sata-0";
		avdd-supply = <&reg_1v05_vdd>;
		hvdd-supply = <&reg_module_3v3>;
		vddio-supply = <&reg_1v05_vdd>;
	};

	usb@70090000 {
		/* USBO1, USBO1 (SS), USBH2, USBH4 and USBH4 (SS) */
		phys = <&{/padctl@7009f000/pads/usb2/lanes/usb2-0}>,
		       <&{/padctl@7009f000/pads/pcie/lanes/pcie-1}>,
		       <&{/padctl@7009f000/pads/usb2/lanes/usb2-1}>,
		       <&{/padctl@7009f000/pads/usb2/lanes/usb2-2}>,
		       <&{/padctl@7009f000/pads/pcie/lanes/pcie-0}>;
		phy-names = "usb2-0", "usb3-1", "usb2-1", "usb2-2", "usb3-0";
		avddio-pex-supply = <&reg_1v05_vdd>;
		avdd-pll-erefe-supply = <&reg_1v05_avdd>;
		avdd-pll-utmip-supply = <&reg_1v8_vddio>;
		avdd-usb-ss-pll-supply = <&reg_1v05_vdd>;
		avdd-usb-supply = <&reg_module_3v3>;
		dvddio-pex-supply = <&reg_1v05_vdd>;
		hvdd-usb-ss-pll-e-supply = <&reg_module_3v3>;
		hvdd-usb-ss-supply = <&reg_module_3v3>;
	};

	padctl@7009f000 {
		avdd-pll-utmip-supply = <&reg_1v8_vddio>;
		avdd-pll-erefe-supply = <&reg_1v05_avdd>;
		avdd-pex-pll-supply = <&reg_1v05_vdd>;
		hvdd-pex-pll-e-supply = <&reg_module_3v3>;

		pads {
			usb2 {
				status = "okay";

				lanes {
					usb2-0 {
						status = "okay";
						nvidia,function = "xusb";
					};

					usb2-1 {
						status = "okay";
						nvidia,function = "xusb";
					};

					usb2-2 {
						status = "okay";
						nvidia,function = "xusb";
					};
				};
			};

			pcie {
				status = "okay";

				lanes {
					pcie-0 {
						status = "okay";
						nvidia,function = "usb3-ss";
					};

					pcie-1 {
						status = "okay";
						nvidia,function = "usb3-ss";
					};

					pcie-2 {
						status = "okay";
						nvidia,function = "pcie";
					};

					pcie-3 {
						status = "okay";
						nvidia,function = "pcie";
					};

					pcie-4 {
						status = "okay";
						nvidia,function = "pcie";
					};
				};
			};

			sata {
				status = "okay";

				lanes {
					sata-0 {
						status = "okay";
						nvidia,function = "sata";
					};
				};
			};
		};

		ports {
			/* USBO1 */
			usb2-0 {
				status = "okay";
				mode = "otg";
				vbus-supply = <&reg_usbo1_vbus>;
			};

			/* USBH2 */
			usb2-1 {
				status = "okay";
				mode = "host";
				vbus-supply = <&reg_usbh_vbus>;
			};

			/* USBH4 */
			usb2-2 {
				status = "okay";
				mode = "host";
				vbus-supply = <&reg_usbh_vbus>;
			};

			usb3-0 {
				status = "okay";
				nvidia,usb2-companion = <2>;
				vbus-supply = <&reg_usbh_vbus>;
			};

			usb3-1 {
				status = "okay";
				nvidia,usb2-companion = <0>;
				vbus-supply = <&reg_usbo1_vbus>;
			};
		};
	};

	/* eMMC */
	sdhci@700b0600 {
		status = "okay";
		bus-width = <8>;
		non-removable;
		vmmc-supply = <&reg_module_3v3>; /* VCC */
		vqmmc-supply = <&reg_1v8_vddio>; /* VCCQ */
		mmc-ddr-1_8v;
	};

	/* CPU DFLL clock */
	clock@70110000 {
		status = "okay";
		nvidia,i2c-fs-rate = <400000>;
		vdd-cpu-supply = <&reg_vdd_cpu>;
	};

	ahub@70300000 {
		i2s@70301200 {
			status = "okay";
		};
	};

	clk32k_in: osc3 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
	};

	cpus {
		cpu@0 {
			vdd-cpu-supply = <&reg_vdd_cpu>;
		};
	};

	reg_1v05_avdd_hdmi_pll: regulator-1v05-avdd-hdmi-pll {
		compatible = "regulator-fixed";
		regulator-name = "+V1.05_AVDD_HDMI_PLL";
		regulator-min-microvolt = <1050000>;
		regulator-max-microvolt = <1050000>;
		gpio = <&gpio TEGRA_GPIO(H, 7) GPIO_ACTIVE_LOW>;
		vin-supply = <&reg_1v05_vdd>;
	};

	reg_3v3_mxm: regulator-3v3-mxm {
		compatible = "regulator-fixed";
		regulator-name = "+V3.3_MXM";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
	};

	reg_3v3_avdd_hdmi: regulator-3v3-avdd-hdmi {
		compatible = "regulator-fixed";
		regulator-name = "+V3.3_AVDD_HDMI";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		vin-supply = <&reg_1v05_vdd>;
	};

	reg_module_3v3: regulator-module-3v3 {
		compatible = "regulator-fixed";
		regulator-name = "+V3.3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
		regulator-boot-on;
		/* PWR_EN_+V3.3 */
		gpio = <&pmic 2 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&reg_3v3_mxm>;
	};

	reg_module_3v3_audio: regulator-module-3v3-audio {
		compatible = "regulator-fixed";
		regulator-name = "+V3.3_AUDIO_AVDD_S";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	sound {
		compatible = "toradex,tegra-audio-sgtl5000-apalis_tk1",
			     "nvidia,tegra-audio-sgtl5000";
		nvidia,model = "Toradex Apalis TK1";
		nvidia,audio-routing =
			"Headphone Jack", "HP_OUT",
			"LINE_IN", "Line In Jack",
			"MIC_IN", "Mic Jack";
		nvidia,i2s-controller = <&tegra_i2s2>;
		nvidia,audio-codec = <&sgtl5000>;
		clocks = <&tegra_car TEGRA124_CLK_PLL_A>,
			 <&tegra_car TEGRA124_CLK_PLL_A_OUT0>,
			 <&tegra_car TEGRA124_CLK_EXTERN1>;
		clock-names = "pll_a", "pll_a_out0", "mclk";
	};

	thermal-zones {
		cpu {
			trips {
				cpu-shutdown-trip {
					temperature = <101000>;
					hysteresis = <0>;
					type = "critical";
				};
			};
		};

		mem {
			trips {
				mem-shutdown-trip {
					temperature = <101000>;
					hysteresis = <0>;
					type = "critical";
				};
			};
		};

		gpu {
			trips {
				gpu-shutdown-trip {
					temperature = <101000>;
					hysteresis = <0>;
					type = "critical";
				};
			};
		};
	};
};

&gpio {
	/* I210 Gigabit Ethernet Controller Reset */
	lan-reset-n {
		gpio-hog;
		gpios = <TEGRA_GPIO(S, 2) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "LAN_RESET_N";
	};

	/* Control MXM3 pin 26 Reset Module Output Carrier Input */
	reset-moci-ctrl {
		gpio-hog;
		gpios = <TEGRA_GPIO(U, 4) GPIO_ACTIVE_HIGH>;
		output-high;
		line-name = "RESET_MOCI_CTRL";
	};
};
