// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014-15 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

/include/ "skeleton_hs_idu.dtsi"

/ {
	model = "snps,nsim_hs-smp";
	compatible = "snps,nsim_hs";
	interrupt-parent = <&core_intc>;

	chosen {
		bootargs = "earlycon=arc_uart,mmio32,0xc0fc1000,115200n8 console=ttyARC0,115200n8 print-fatal-signals=1";
	};

	aliases {
		serial0 = &arcuart0;
	};

	fpga {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* child and parent address space 1:1 mapped */
		ranges;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <80000000>;
		};

		core_intc: core-interrupt-controller {
			compatible = "snps,archs-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		idu_intc: idu-interrupt-controller {
			compatible = "snps,archs-idu-intc";
			interrupt-controller;
			interrupt-parent = <&core_intc>;
			#interrupt-cells = <1>;
		};

		arcuart0: serial@c0fc1000 {
			compatible = "snps,arc-uart";
			reg = <0xc0fc1000 0x100>;
			interrupt-parent = <&idu_intc>;
			interrupts = <0>;
			clock-frequency = <80000000>;
			current-speed = <115200>;
			status = "okay";
		};

		arcpct0: pct {
			compatible = "snps,archs-pct";
			#interrupt-cells = <1>;
			interrupts = <20>;
		};
	};
};
