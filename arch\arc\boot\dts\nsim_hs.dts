// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2014-15 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

/include/ "skeleton_hs.dtsi"

/ {
	model = "snps,nsim_hs";
	compatible = "snps,nsim_hs";
	#address-cells = <2>;
	#size-cells = <2>;
	interrupt-parent = <&core_intc>;

	memory {
		device_type = "memory";
		/* CONFIG_LINUX_RAM_BASE needs to match low mem start */
		reg = <0x0 0x80000000 0x0 0x20000000	/* 512 MB low mem */
		       0x1 0x00000000 0x0 0x40000000>;	/* 1 GB highmem */
	};

	chosen {
		bootargs = "earlycon=arc_uart,mmio32,0xc0fc1000,115200n8 console=ttyARC0,115200n8 print-fatal-signals=1";
	};

	aliases {
		serial0 = &arcuart0;
	};

	fpga {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* only perip space at end of low mem accessible
			 bus addr,   parent bus addr, size */
		ranges = <0x80000000 0x0 0x80000000 0x80000000>;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <80000000>;
		};

		core_intc: core-interrupt-controller {
			compatible = "snps,archs-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		arcuart0: serial@c0fc1000 {
			compatible = "snps,arc-uart";
			reg = <0xc0fc1000 0x100>;
			interrupts = <24>;
			clock-frequency = <80000000>;
			current-speed = <115200>;
			status = "okay";
		};

		arcpct0: pct {
			compatible = "snps,archs-pct";
			#interrupt-cells = <1>;
			interrupts = <20>;
		};
	};
};
