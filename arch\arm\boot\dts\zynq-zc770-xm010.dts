// SPDX-License-Identifier: GPL-2.0+
/*
 * Xilinx ZC770 XM010 board DTS
 *
 * Copyright (C) 2013-2018 Xilinx, Inc.
 */
/dts-v1/;
#include "zynq-7000.dtsi"

/ {
	model = "Xilinx ZC770 XM010 board";
	compatible = "xlnx,zynq-zc770-xm010", "xlnx,zynq-7000";

	aliases {
		ethernet0 = &gem0;
		i2c0 = &i2c0;
		serial0 = &uart1;
		spi1 = &spi1;
	};

	chosen {
		bootargs = "";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};

	usb_phy0: phy0 {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
	};
};

&can0 {
	status = "okay";
};

&gem0 {
	status = "okay";
	phy-mode = "rgmii-id";
	phy-handle = <&ethernet_phy>;

	ethernet_phy: ethernet-phy@7 {
		reg = <7>;
		device_type = "ethernet-phy";
	};
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;

	eeprom: eeprom@52 {
		compatible = "atmel,24c02";
		reg = <0x52>;
	};

};

&sdhci0 {
	status = "okay";
};

&spi1 {
	status = "okay";
	num-cs = <4>;
	is-decoded-cs = <0>;
	flash@1 {
		compatible = "sst25wf080", "jedec,spi-nor";
		reg = <1>;
		spi-max-frequency = <1000000>;
		partitions {
			compatible = "fixed-partitions";
			#address-cells = <1>;
			#size-cells = <1>;
			partition@0 {
				label = "data";
				reg = <0x0 0x100000>;
			};
		};
	};
};

&uart1 {
	status = "okay";
};

&usb0 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy0>;
};
