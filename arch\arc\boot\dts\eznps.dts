// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright(c) 2015 EZchip Technologies.
 */

/dts-v1/;

/ {
	compatible = "ezchip,arc-nps";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&intc>;
	present-cpus = "0-1,16-17";
	possible-cpus = "0-4095";

	aliases {
		ethernet0 = &gmac0;
	};

	chosen {
		bootargs = "earlycon=uart8250,mmio32be,0xf7209000,115200n8 console=ttyS0,115200n8";
	};

	memory {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;	/* 512M */
	};

	clocks {
		sysclk: sysclk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <83333333>;
		};
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* child and parent address space 1:1 mapped */
		ranges;

		intc: interrupt-controller {
			compatible = "ezchip,nps400-ic";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		timer0: timer_clkevt {
			compatible = "snps,arc-timer";
			interrupts = <3>;
			clocks = <&sysclk>;
		};

		timer1: timer_clksrc {
			compatible = "ezchip,nps400-timer";
			clocks = <&sysclk>;
			clock-names="sysclk";
		};

		uart@f7209000 {
			compatible = "snps,dw-apb-uart";
			device_type = "serial";
			reg = <0xf7209000 0x100>;
			interrupts = <6>;
			clocks = <&sysclk>;
			clock-names="baudclk";
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			native-endian;
		};

		gmac0: ethernet@f7470000 {
			compatible = "ezchip,nps-mgt-enet";
			reg = <0xf7470000 0x1940>;
			interrupts = <7>;
			/* Filled in by U-Boot */
			mac-address = [ 00 C0 00 F0 04 03 ];
		};
	};
};
