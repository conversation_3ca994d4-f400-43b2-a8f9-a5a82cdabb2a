// SPDX-License-Identifier: GPL-2.0
/*
 *  Copyright (C) 2015 <PERSON> <<EMAIL>>
 *  Copyright (C) 2017 <PERSON> <<EMAIL>>
 *
 *  Based on zynq-zed.dts which is:
 *  Copyright (C) 2011 - 2014 Xilinx
 *  Copyright (C) 2012 National Instruments Corp.
 *
 */

/dts-v1/;
/include/ "zynq-7000.dtsi"

/ {
	model = "Zynq Z-Turn MYIR Board";
	compatible = "myir,zynq-zturn", "xlnx,zynq-7000";

	aliases {
		ethernet0 = &gem0;
		serial0 = &uart1;
		serial1 = &uart0;
		mmc0 = &sdhci0;
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	gpio-leds {
		compatible = "gpio-leds";
		usr-led1 {
			label = "usr-led1";
			gpios = <&gpio0 0x0 0x1>;
			default-state = "off";
		};

		usr-led2 {
			label = "usr-led2";
			gpios = <&gpio0 0x9 0x1>;
			default-state = "off";
		};
	};

	gpio-keys {
		compatible = "gpio-keys";
		autorepeat;
		K1 {
			label = "K1";
			gpios = <&gpio0 0x32 0x1>;
			linux,code = <0x66>;
			wakeup-source;
			autorepeat;
		};
	};
};

&clkc {
	ps-clk-frequency = <33333333>;
};

&gem0 {
	status = "okay";
	phy-mode = "rgmii-id";
	phy-handle = <&ethernet_phy>;

	ethernet_phy: ethernet-phy@0 {
		reg = <0x0>;
	};
};

&sdhci0 {
	status = "okay";
};

&uart0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&usb0 {
	status = "okay";
	dr_mode = "host";
};

&can0 {
	status = "okay";
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;

	stlm75@49 {
		status = "okay";
		compatible = "lm75";
		reg = <0x49>;
	};

	accelerometer@53 {
		compatible = "adi,adxl345", "adxl345", "adi,adxl34x", "adxl34x";
		reg = <0x53>;
		interrupt-parent = <&intc>;
		interrupts = <0x0 0x1e 0x4>;
	};
};
