// SPDX-License-Identifier: GPL-2.0-only
/*
 * Abilis Systems TB101 Development Kit PCB device tree
 *
 * Copyright (C) Abilis Systems 2013
 *
 * Author: <PERSON> <<EMAIL>>
 */

/dts-v1/;

/include/ "abilis_tb101.dtsi"

/ {
	model = "abilis,tb101";
	chosen {
		bootargs = "earlycon=uart8250,mmio32,0xff100000,9600n8 console=ttyS0,9600n8";
	};

	aliases { };

	memory {
		device_type = "memory";
		reg = <0x80000000 0x08000000>;	/* 128M */
	};

	soc100 {
		uart@ff100000 {
			pinctrl-names = "default";
			pinctrl-0 = <&pctl_uart0>;
		};
		ethernet@fe100000 {
			phy-mode = "rgmii";
		};

		i2c0: i2c@ff120000 {
			i2c-sda-hold-time-ns = <432>;
		};
		i2c1: i2c@ff121000 {
			i2c-sda-hold-time-ns = <432>;
		};
		i2c2: i2c@ff122000 {
			i2c-sda-hold-time-ns = <432>;
		};
		i2c3: i2c@ff123000 {
			i2c-sda-hold-time-ns = <432>;
		};
		i2c4: i2c@ff124000 {
			i2c-sda-hold-time-ns = <432>;
		};

		leds {
			compatible = "gpio-leds";
			power {
				label = "Power";
				gpios = <&gpioi 0 0>;
				linux,default-trigger = "default-on";
			};
			heartbeat {
				label = "Heartbeat";
				gpios = <&gpioi 1 0>;
				linux,default-trigger = "heartbeat";
			};
			led2 {
				label = "LED2";
				gpios = <&gpioi 2 0>;
				default-state = "off";
			};
			led3 {
				label = "LED3";
				gpios = <&gpioi 3 0>;
				default-state = "off";
			};
			led4 {
				label = "LED4";
				gpios = <&gpioi 4 0>;
				default-state = "off";
			};
			led5 {
				label = "LED5";
				gpios = <&gpioi 5 0>;
				default-state = "off";
			};
			led6 {
				label = "LED6";
				gpios = <&gpioi 6 0>;
				default-state = "off";
			};
			led7 {
				label = "LED7";
				gpios = <&gpioi 7 0>;
				default-state = "off";
			};
			led8 {
				label = "LED8";
				gpios = <&gpioi 8 0>;
				default-state = "off";
			};
			led9 {
				label = "LED9";
				gpios = <&gpioi 9 0>;
				default-state = "off";
			};
			led10 {
				label = "LED10";
				gpios = <&gpioi 10 0>;
				default-state = "off";
			};
			led11 {
				label = "LED11";
				gpios = <&gpioi 11 0>;
				default-state = "off";
			};
		};
	};
};
