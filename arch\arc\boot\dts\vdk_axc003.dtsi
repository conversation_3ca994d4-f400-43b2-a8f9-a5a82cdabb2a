// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2013, 2014 Synopsys, Inc. (www.synopsys.com)
 */

/*
 * Device tree for AXC003 CPU card: HS38x UP configuration (VDK version)
 */

/include/ "skeleton_hs.dtsi"

/ {
	compatible = "snps,arc";
	#address-cells = <1>;
	#size-cells = <1>;

	cpu_card {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		ranges = <0x00000000 0xf0000000 0x10000000>;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
		};

		core_intc: archs-intc@cpu {
			compatible = "snps,archs-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		debug_uart: dw-apb-uart@5000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x5000 0x100>;
			clock-frequency = <2403200>;
			interrupt-parent = <&core_intc>;
			interrupts = <19>;
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
		};

	};

	mb_intc: dw-apb-ictl@e0012000 {
		#interrupt-cells = <1>;
		compatible = "snps,dw-apb-ictl";
		reg = < 0xe0012000 0x200 >;
		interrupt-controller;
		interrupt-parent = <&core_intc>;
		interrupts = < 18 >;
	};

	memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x00000000 0x80000000 0x40000000>;
		device_type = "memory";
		reg = <0x80000000 0x20000000>;	/* 512MiB */
	};
};
