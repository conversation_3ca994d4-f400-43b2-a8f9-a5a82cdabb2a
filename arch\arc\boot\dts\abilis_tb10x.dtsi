// SPDX-License-Identifier: GPL-2.0-only
/*
 * Abilis Systems TB10X SOC device tree
 *
 * Copyright (C) Abilis Systems 2013
 *
 * Author: <PERSON> <<EMAIL>>
 */


/ {
	compatible		= "abilis,arc-tb10x";
	#address-cells		= <1>;
	#size-cells		= <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu@0 {
			device_type = "cpu";
			compatible = "snps,arc770d";
			reg = <0>;
		};
	};

	/* TIMER0 with interrupt for clockevent */
	timer0 {
		compatible = "snps,arc-timer";
		interrupts = <3>;
		interrupt-parent = <&intc>;
		clocks = <&cpu_clk>;
	};

	/* TIMER1 for free running clocksource */
	timer1 {
		compatible = "snps,arc-timer";
		clocks = <&cpu_clk>;
	};

	soc100 {
		#address-cells	= <1>;
		#size-cells	= <1>;
		device_type	= "soc";
		ranges		= <0xfe000000 0xfe000000 0x02000000
				0x000f0000 0x000f0000 0x00010000>;
		compatible	= "abilis,tb10x", "simple-bus";

		pll0: oscillator {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-output-names = "pll0";
		};
		cpu_clk: clkdiv_cpu {
			compatible = "fixed-factor-clock";
			#clock-cells = <0>;
			clocks = <&pll0>;
			clock-output-names = "cpu_clk";
		};
		ahb_clk: clkdiv_ahb {
			compatible = "fixed-factor-clock";
			#clock-cells = <0>;
			clocks = <&pll0>;
			clock-output-names = "ahb_clk";
		};

		iomux: iomux@ff10601c {
			compatible = "abilis,tb10x-iomux";
			#gpio-range-cells = <3>;
			reg = <0xff10601c 0x4>;
		};

		intc: interrupt-controller {
			compatible = "snps,arc700-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};
		tb10x_ictl: pic@fe002000 {
			compatible = "abilis,tb10x-ictl";
			reg = <0xfe002000 0x20>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupt-parent = <&intc>;
			interrupts = <5 6 7 8 9 10 11 12 13 14 15 16 17 18 19
					20 21 22 23 24 25 26 27 28 29 30 31>;
		};

		uart@ff100000 {
			compatible = "snps,dw-apb-uart";
			reg = <0xff100000 0x100>;
			clock-frequency = <166666666>;
			interrupts = <25 8>;
			reg-shift = <2>;
			reg-io-width = <4>;
			interrupt-parent = <&tb10x_ictl>;
		};
		ethernet@fe100000 {
			compatible = "snps,dwmac-3.70a","snps,dwmac";
			reg = <0xfe100000 0x1058>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <6 8>;
			interrupt-names = "macirq";
			clocks = <&ahb_clk>;
			clock-names = "stmmaceth";
		};
		dma@fe000000 {
			compatible = "snps,dma-spear1340";
			reg = <0xfe000000 0x400>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <14 8>;
			dma-channels = <6>;
			dma-requests = <0>;
			dma-masters = <1>;
			#dma-cells = <3>;
			chan_allocation_order = <0>;
			chan_priority = <1>;
			block_size = <0x7ff>;
			data-width = <4>;
			clocks = <&ahb_clk>;
			clock-names = "hclk";
			multi-block = <1 1 1 1 1 1>;
		};

		i2c0: i2c@ff120000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "snps,designware-i2c";
			reg = <0xff120000 0x1000>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <12 8>;
			clocks = <&ahb_clk>;
		};
		i2c1: i2c@ff121000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "snps,designware-i2c";
			reg = <0xff121000 0x1000>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <12 8>;
			clocks = <&ahb_clk>;
		};
		i2c2: i2c@ff122000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "snps,designware-i2c";
			reg = <0xff122000 0x1000>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <12 8>;
			clocks = <&ahb_clk>;
		};
		i2c3: i2c@ff123000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "snps,designware-i2c";
			reg = <0xff123000 0x1000>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <12 8>;
			clocks = <&ahb_clk>;
		};
		i2c4: i2c@ff124000 {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "snps,designware-i2c";
			reg = <0xff124000 0x1000>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <12 8>;
			clocks = <&ahb_clk>;
		};

		spi0: spi@fe010000 {
			#address-cells = <1>;
			#size-cells = <0>;
			cell-index = <0>;
			compatible = "abilis,tb100-spi";
			num-cs = <1>;
			reg = <0xfe010000 0x20>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <26 8>;
			clocks = <&ahb_clk>;
		};
		spi1: spi@fe011000 {
			#address-cells = <1>;
			#size-cells = <0>;
			cell-index = <1>;
			compatible = "abilis,tb100-spi";
			num-cs = <2>;
			reg = <0xfe011000 0x20>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <10 8>;
			clocks = <&ahb_clk>;
		};

		tb10x_tsm: tb10x-tsm@ff316000 {
			compatible = "abilis,tb100-tsm";
			reg = <0xff316000 0x400>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <17 8>;
			output-clkdiv = <4>;
			global-packet-delay = <0x21>;
			port-packet-delay = <0>;
		};
		tb10x_stream_proc: tb10x-stream-proc {
			compatible = "abilis,tb100-streamproc";
			reg =   <0xfff00000 0x200>,
				<0x000f0000 0x10000>,
				<0xfff00200 0x105>,
				<0xff10600c 0x1>,
				<0xfe001018 0x1>;
			reg-names =     "mbox",
					"sp_iccm",
					"mbox_irq",
					"cpuctrl",
					"a6it_int_force";
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <20 2>, <19 2>;
			interrupt-names = "cmd_irq", "event_irq";
		};
		tb10x_mdsc0: tb10x-mdscr@ff300000 {
			compatible = "abilis,tb100-mdscr";
			reg = <0xff300000 0x7000>;
			tb100-mdscr-manage-tsin;
		};
		tb10x_mscr0: tb10x-mdscr@ff307000 {
			compatible = "abilis,tb100-mdscr";
			reg = <0xff307000 0x7000>;
		};
		tb10x_scr0: tb10x-mdscr@ff30e000 {
			compatible = "abilis,tb100-mdscr";
			reg = <0xff30e000 0x4000>;
			tb100-mdscr-manage-tsin;
		};
		tb10x_scr1: tb10x-mdscr@ff312000 {
			compatible = "abilis,tb100-mdscr";
			reg = <0xff312000 0x4000>;
			tb100-mdscr-manage-tsin;
		};
		tb10x_wfb: tb10x-wfb@ff319000 {
			compatible = "abilis,tb100-wfb";
			reg = <0xff319000 0x1000>;
			interrupt-parent = <&tb10x_ictl>;
			interrupts = <16 8>;
		};
	};
};
