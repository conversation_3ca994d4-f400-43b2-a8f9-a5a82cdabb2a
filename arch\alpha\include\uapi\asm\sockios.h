/* SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note */
#ifndef _ASM_ALPHA_SOCKIOS_H
#define _ASM_ALPHA_SOCKIOS_H

/* Socket-level I/O control calls. */

#define FIOGETOWN	_IOR('f', 123, int)
#define FIOSETOWN 	_IOW('f', 124, int)

#define SIOCATMARK	_IOR('s', 7, int)
#define SIOCSPGRP	_IOW('s', 8, pid_t)
#define SIOCGPGRP	_IOR('s', 9, pid_t)

#define SIOCGSTAMP_OLD	0x8906		/* Get stamp (timeval) */
#define SIOCGSTAMPNS_OLD 0x8907		/* Get stamp (timespec) */

#endif /* _ASM_ALPHA_SOCKIOS_H */
