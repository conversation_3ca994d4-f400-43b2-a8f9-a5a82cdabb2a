// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * vt8500.dtsi - Device tree file for VIA VT8500 SoC
 *
 * Copyright (C) 2012 <PERSON> <<EMAIL>>
 */

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	compatible = "via,vt8500";

	cpus {
		#address-cells = <0>;
		#size-cells = <0>;

		cpu {
			device_type = "cpu";
			compatible = "arm,arm926ej-s";
		};
	};

	memory {
		device_type = "memory";
		reg = <0x0 0x0>;
	};

	aliases {
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
	};

	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		ranges;
		interrupt-parent = <&intc>;

		intc: interrupt-controller@d8140000 {
			compatible = "via,vt8500-intc";
			interrupt-controller;
			reg = <0xd8140000 0x10000>;
			#interrupt-cells = <1>;
		};

		pinctrl: pinctrl@d8110000 {
			compatible = "via,vt8500-pinctrl";
			reg = <0xd8110000 0x10000>;
			interrupt-controller;
			#interrupt-cells = <2>;
			gpio-controller;
			#gpio-cells = <2>;
		};

		pmc@d8130000 {
			compatible = "via,vt8500-pmc";
			reg = <0xd8130000 0x1000>;

			clocks {
				#address-cells = <1>;
				#size-cells = <0>;

				ref24: ref24M {
					#clock-cells = <0>;
					compatible = "fixed-clock";
					clock-frequency = <24000000>;
				};

				clkuart0: uart0 {
					#clock-cells = <0>;
					compatible = "via,vt8500-device-clock";
					clocks = <&ref24>;
					enable-reg = <0x250>;
					enable-bit = <1>;
				};

				clkuart1: uart1 {
					#clock-cells = <0>;
					compatible = "via,vt8500-device-clock";
					clocks = <&ref24>;
					enable-reg = <0x250>;
					enable-bit = <2>;
				};

				clkuart2: uart2 {
					#clock-cells = <0>;
					compatible = "via,vt8500-device-clock";
					clocks = <&ref24>;
					enable-reg = <0x250>;
					enable-bit = <3>;
				};

				clkuart3: uart3 {
					#clock-cells = <0>;
					compatible = "via,vt8500-device-clock";
					clocks = <&ref24>;
					enable-reg = <0x250>;
					enable-bit = <4>;
				};
			};
		};

		timer@d8130100 {
			compatible = "via,vt8500-timer";
			reg = <0xd8130100 0x28>;
			interrupts = <36>;
		};

		ehci@d8007900 {
			compatible = "via,vt8500-ehci";
			reg = <0xd8007900 0x200>;
			interrupts = <43>;
		};

		uhci@d8007b00 {
			compatible = "platform-uhci";
			reg = <0xd8007b00 0x200>;
			interrupts = <43>;
		};

		fb: fb@d8050800 {
			compatible = "via,vt8500-fb";
			reg = <0xd800e400 0x400>;
			interrupts = <12>;
		};

		ge_rops@d8050400 {
			compatible = "wm,prizm-ge-rops";
			reg = <0xd8050400 0x100>;
		};

		uart0: serial@d8200000 {
			compatible = "via,vt8500-uart";
			reg = <0xd8200000 0x1040>;
			interrupts = <32>;
			clocks = <&clkuart0>;
			status = "disabled";
		};

		uart1: serial@d82b0000 {
			compatible = "via,vt8500-uart";
			reg = <0xd82b0000 0x1040>;
			interrupts = <33>;
			clocks = <&clkuart1>;
			status = "disabled";
		};

		uart2: serial@d8210000 {
			compatible = "via,vt8500-uart";
			reg = <0xd8210000 0x1040>;
			interrupts = <47>;
			clocks = <&clkuart2>;
			status = "disabled";
		};

		uart3: serial@d82c0000 {
			compatible = "via,vt8500-uart";
			reg = <0xd82c0000 0x1040>;
			interrupts = <50>;
			clocks = <&clkuart3>;
			status = "disabled";
		};

		rtc@d8100000 {
			compatible = "via,vt8500-rtc";
			reg = <0xd8100000 0x10000>;
			interrupts = <48>;
		};

		ethernet@d8004000 {
			compatible = "via,vt8500-rhine";
			reg = <0xd8004000 0x100>;
			interrupts = <10>;
		};
	};
};
