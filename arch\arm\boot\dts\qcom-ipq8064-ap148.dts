// SPDX-License-Identifier: GPL-2.0
#include "qcom-ipq8064-v1.0.dtsi"

/ {
	model = "Qualcomm Technologies, Inc. IPQ8064/AP-148";
	compatible = "qcom,ipq8064-ap148";

	soc {
		pinmux@800000 {
			i2c4_pins: i2c4_pinmux {
				pins = "gpio12", "gpio13";
				function = "gsbi4";
				bias-disable;
			};

			buttons_pins: buttons_pins {
				mux {
					pins = "gpio54", "gpio65";
					drive-strength = <2>;
					bias-pull-up;
				};
			};
		};

		gsbi@16300000 {
			i2c@16380000 {
				status = "ok";
				clock-frequency = <200000>;
				pinctrl-0 = <&i2c4_pins>;
				pinctrl-names = "default";
			};
		};
	};
};
