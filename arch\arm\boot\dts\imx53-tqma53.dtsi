// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * Copyright 2012 <PERSON><PERSON><PERSON> <<EMAIL>>, Pengutronix
 * Copyright 2012 Steffen Trumtrar <<EMAIL>>, Pengutronix
 */

#include "imx53.dtsi"

/ {
	model = "TQ TQMa53";
	compatible = "tq,tqma53", "fsl,imx53";

	memory@70000000 {
		device_type = "memory";
		reg = <0x70000000 0x40000000>; /* Up to 1GiB */
	};

	regulators {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <0>;

		reg_3p3v: regulator@0 {
			compatible = "regulator-fixed";
			reg = <0>;
			regulator-name = "3P3V";
			regulator-min-microvolt = <3300000>;
			regulator-max-microvolt = <3300000>;
			regulator-always-on;
		};
	};
};

&esdhc2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_esdhc2>,
		    <&pinctrl_esdhc2_cdwp>;
	vmmc-supply = <&reg_3p3v>;
	wp-gpios = <&gpio1 2 GPIO_ACTIVE_HIGH>;
	cd-gpios = <&gpio1 4 GPIO_ACTIVE_LOW>;
	status = "disabled";
};

&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart3>;
	status = "disabled";
};

&ecspi1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_ecspi1>;
	cs-gpios = <&gpio2 30 0>, <&gpio3 19 0>,
		   <&gpio3 24 0>, <&gpio3 25 0>;
	status = "disabled";
};

&esdhc3 { /* EMMC */
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_esdhc3>;
	vmmc-supply = <&reg_3p3v>;
	non-removable;
	bus-width = <8>;
	status = "okay";
};

&iomuxc {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_hog>;

	imx53-tqma53 {
		pinctrl_hog: hoggrp {
			fsl,pins = <
				 MX53_PAD_GPIO_0__CCM_SSI_EXT1_CLK 0x80000000 /* SSI_MCLK */
				 MX53_PAD_PATA_DA_1__GPIO7_7     0x80000000 /* LCD_BLT_EN */
				 MX53_PAD_PATA_DA_2__GPIO7_8     0x80000000 /* LCD_RESET */
				 MX53_PAD_PATA_DATA5__GPIO2_5    0x80000000 /* LCD_POWER */
				 MX53_PAD_PATA_DATA6__GPIO2_6    0x80000000 /* PMIC_INT */
				 MX53_PAD_PATA_DATA14__GPIO2_14  0x80000000 /* CSI_RST */
				 MX53_PAD_PATA_DATA15__GPIO2_15  0x80000000 /* CSI_PWDN */
				 MX53_PAD_GPIO_19__GPIO4_5 	 0x80000000 /* #SYSTEM_DOWN */
				 MX53_PAD_GPIO_3__GPIO1_3        0x80000000
				 MX53_PAD_PATA_DA_0__GPIO7_6	 0x80000000 /* #PHY_RESET */
				 MX53_PAD_GPIO_1__PWM2_PWMO	 0x80000000 /* LCD_CONTRAST */
			>;
		};

		pinctrl_audmux: audmuxgrp {
			fsl,pins = <
				MX53_PAD_KEY_COL0__AUDMUX_AUD5_TXC	0x80000000
				MX53_PAD_KEY_ROW0__AUDMUX_AUD5_TXD	0x80000000
				MX53_PAD_KEY_COL1__AUDMUX_AUD5_TXFS	0x80000000
				MX53_PAD_KEY_ROW1__AUDMUX_AUD5_RXD	0x80000000
			>;
		};

		pinctrl_can1: can1grp {
			fsl,pins = <
				MX53_PAD_KEY_COL2__CAN1_TXCAN		0x80000000
				MX53_PAD_KEY_ROW2__CAN1_RXCAN		0x80000000
			>;
		};

		pinctrl_can2: can2grp {
			fsl,pins = <
				MX53_PAD_KEY_COL4__CAN2_TXCAN		0x80000000
				MX53_PAD_KEY_ROW4__CAN2_RXCAN		0x80000000
			>;
		};

		pinctrl_cspi: cspigrp {
			fsl,pins = <
				MX53_PAD_SD1_DATA0__CSPI_MISO		0x1d5
				MX53_PAD_SD1_CMD__CSPI_MOSI		0x1d5
				MX53_PAD_SD1_CLK__CSPI_SCLK		0x1d5
			>;
		};

		pinctrl_ecspi1: ecspi1grp {
			fsl,pins = <
				MX53_PAD_EIM_D16__ECSPI1_SCLK		0x80000000
				MX53_PAD_EIM_D17__ECSPI1_MISO		0x80000000
				MX53_PAD_EIM_D18__ECSPI1_MOSI		0x80000000
			>;
		};

		pinctrl_esdhc2: esdhc2grp {
			fsl,pins = <
				MX53_PAD_SD2_CMD__ESDHC2_CMD		0x1d5
				MX53_PAD_SD2_CLK__ESDHC2_CLK		0x1d5
				MX53_PAD_SD2_DATA0__ESDHC2_DAT0		0x1d5
				MX53_PAD_SD2_DATA1__ESDHC2_DAT1		0x1d5
				MX53_PAD_SD2_DATA2__ESDHC2_DAT2		0x1d5
				MX53_PAD_SD2_DATA3__ESDHC2_DAT3		0x1d5
			>;
		};

		pinctrl_esdhc2_cdwp: esdhc2cdwp {
			fsl,pins = <
				MX53_PAD_GPIO_4__GPIO1_4	0x80000000 /* SD2_CD */
				MX53_PAD_GPIO_2__GPIO1_2	0x80000000 /* SD2_WP */
			>;
		};

		pinctrl_esdhc3: esdhc3grp {
			fsl,pins = <
				MX53_PAD_PATA_DATA8__ESDHC3_DAT0	0x1d5
				MX53_PAD_PATA_DATA9__ESDHC3_DAT1	0x1d5
				MX53_PAD_PATA_DATA10__ESDHC3_DAT2	0x1d5
				MX53_PAD_PATA_DATA11__ESDHC3_DAT3	0x1d5
				MX53_PAD_PATA_DATA0__ESDHC3_DAT4	0x1d5
				MX53_PAD_PATA_DATA1__ESDHC3_DAT5	0x1d5
				MX53_PAD_PATA_DATA2__ESDHC3_DAT6	0x1d5
				MX53_PAD_PATA_DATA3__ESDHC3_DAT7	0x1d5
				MX53_PAD_PATA_RESET_B__ESDHC3_CMD	0x1d5
				MX53_PAD_PATA_IORDY__ESDHC3_CLK		0x1d5
			>;
		};

		pinctrl_fec: fecgrp {
			fsl,pins = <
				MX53_PAD_FEC_MDC__FEC_MDC		0x80000000
				MX53_PAD_FEC_MDIO__FEC_MDIO		0x80000000
				MX53_PAD_FEC_REF_CLK__FEC_TX_CLK	0x80000000
				MX53_PAD_FEC_RX_ER__FEC_RX_ER		0x80000000
				MX53_PAD_FEC_CRS_DV__FEC_RX_DV		0x80000000
				MX53_PAD_FEC_RXD1__FEC_RDATA_1		0x80000000
				MX53_PAD_FEC_RXD0__FEC_RDATA_0		0x80000000
				MX53_PAD_FEC_TX_EN__FEC_TX_EN		0x80000000
				MX53_PAD_FEC_TXD1__FEC_TDATA_1		0x80000000
				MX53_PAD_FEC_TXD0__FEC_TDATA_0		0x80000000
			>;
		};

		pinctrl_i2c2: i2c2grp {
			fsl,pins = <
				MX53_PAD_KEY_ROW3__I2C2_SDA		0xc0000000
				MX53_PAD_KEY_COL3__I2C2_SCL		0xc0000000
			>;
		};

		pinctrl_i2c3: i2c3grp {
			fsl,pins = <
				MX53_PAD_GPIO_6__I2C3_SDA		0xc0000000
				MX53_PAD_GPIO_5__I2C3_SCL		0xc0000000
			>;
		};

		pinctrl_uart1: uart1grp {
			fsl,pins = <
				MX53_PAD_PATA_DIOW__UART1_TXD_MUX	0x1e4
				MX53_PAD_PATA_DMACK__UART1_RXD_MUX	0x1e4
			>;
		};

		pinctrl_uart2: uart2grp {
			fsl,pins = <
				MX53_PAD_PATA_BUFFER_EN__UART2_RXD_MUX	0x1e4
				MX53_PAD_PATA_DMARQ__UART2_TXD_MUX	0x1e4
			>;
		};

		pinctrl_uart3: uart3grp {
			fsl,pins = <
				MX53_PAD_PATA_CS_0__UART3_TXD_MUX	0x1e4
				MX53_PAD_PATA_CS_1__UART3_RXD_MUX	0x1e4
			>;
		};
	};
};

&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart1>;
	uart-has-rtscts;
	status = "disabled";
};

&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart2>;
	status = "disabled";
};

&can1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_can1>;
	status = "disabled";
};

&can2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_can2>;
	status = "disabled";
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c3>;
	status = "disabled";
};

&cspi {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_cspi>;
	cs-gpios = <&gpio1 18 0>, <&gpio1 19 0>,
		   <&gpio1 21 0>;
	status = "disabled";
};

&i2c2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c2>;
	status = "okay";

	pmic: mc34708@8 {
		compatible = "fsl,mc34708";
		reg = <0x8>;
		fsl,mc13xxx-uses-rtc;
		interrupt-parent = <&gpio2>;
		interrupts = <6 4>; /* PATA_DATA6, active high */
	};

	sensor1: lm75@48 {
		compatible = "lm75";
		reg = <0x48>;
	};

	eeprom: 24c64@50 {
		compatible = "atmel,24c64";
		pagesize = <32>;
		reg = <0x50>;
	};
};

&fec {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_fec>;
	phy-mode = "rmii";
	status = "disabled";
};
