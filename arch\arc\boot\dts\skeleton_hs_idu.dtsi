// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2016 Synopsys, Inc. (www.synopsys.com)
 */

/ {
	compatible = "snps,arc";
	#address-cells = <1>;
	#size-cells = <1>;
	chosen { };
	aliases { };

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <0>;
			clocks = <&core_clk>;
		};
		cpu@1 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <1>;
			clocks = <&core_clk>;
		};
		cpu@2 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <2>;
			clocks = <&core_clk>;
		};
		cpu@3 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <3>;
			clocks = <&core_clk>;
		};
	};

	/* TIMER0 with interrupt for clockevent */
	timer0 {
		compatible = "snps,arc-timer";
		interrupts = <16>;
		interrupt-parent = <&core_intc>;
		clocks = <&core_clk>;
	};

	/* 64-bit Global Free Running Counter */
	gfrc {
		compatible = "snps,archs-timer-gfrc";
		clocks = <&core_clk>;
	};

	memory {
		device_type = "memory";
		reg = <0x80000000 0x10000000>;	/* 256M */
	};
};
