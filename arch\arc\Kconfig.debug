# SPDX-License-Identifier: GPL-2.0

config 16KSTACKS
	bool "Use 16Kb for kernel stacks instead of 8Kb"
	help
	  If you say Y here the kernel will use a  16Kb stacksize for the
	  kernel stack attached to each process/thread. The default is 8K.
	  This increases the resident kernel footprint and will cause less
	  threads to run on the system and also increase the pressure
	  on the VM subsystem for higher order allocations.
