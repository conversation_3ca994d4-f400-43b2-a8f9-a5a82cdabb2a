// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (C) 2011 - 2014 Xilinx
 * Copyright (C) 2016 <PERSON><PERSON> <<EMAIL>>
 */
/dts-v1/;
/include/ "zynq-7000.dtsi"

/ {
	model = "Avnet MicroZed board";
	compatible = "avnet,zynq-microzed", "xlnx,zynq-microzed", "xlnx,zynq-7000";

	aliases {
		ethernet0 = &gem0;
		serial0 = &uart1;
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};

	chosen {
		bootargs = "earlycon";
		stdout-path = "serial0:115200n8";
	};

	usb_phy0: phy0 {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
	};
};

&clkc {
	ps-clk-frequency = <33333333>;
};

&gem0 {
	status = "okay";
	phy-mode = "rgmii-id";
	phy-handle = <&ethernet_phy>;

	ethernet_phy: ethernet-phy@0 {
		reg = <0>;
	};
};

&sdhci0 {
	status = "okay";
};

&uart1 {
	status = "okay";
};

&usb0 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy0>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_usb0_default>;
};

&pinctrl0 {
	pinctrl_usb0_default: usb0-default {
		mux {
			groups = "usb0_0_grp";
			function = "usb0";
		};

		conf {
			groups = "usb0_0_grp";
			slew-rate = <0>;
			io-standard = <1>;
		};

		conf-rx {
			pins = "MIO29", "MIO31", "MIO36";
			bias-high-impedance;
		};

		conf-tx {
			pins = "MIO28", "MIO30", "MIO32", "MIO33", "MIO34",
			       "MIO35", "MIO37", "MIO38", "MIO39";
			bias-disable;
		};
	};
};
