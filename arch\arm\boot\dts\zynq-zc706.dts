// SPDX-License-Identifier: GPL-2.0
/*
 *  Copyright (C) 2011 - 2014 Xilinx
 *  Copyright (C) 2012 National Instruments Corp.
 */
/dts-v1/;
#include "zynq-7000.dtsi"

/ {
	model = "Xilinx ZC706 board";
	compatible = "xlnx,zynq-zc706", "xlnx,zynq-7000";

	aliases {
		ethernet0 = &gem0;
		i2c0 = &i2c0;
		serial0 = &uart1;
		mmc0 = &sdhci0;
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};

	chosen {
		bootargs = "";
		stdout-path = "serial0:115200n8";
	};

	usb_phy0: phy0 {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
	};
};

&clkc {
	ps-clk-frequency = <33333333>;
};

&gem0 {
	status = "okay";
	phy-mode = "rgmii-id";
	phy-handle = <&ethernet_phy>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_gem0_default>;

	ethernet_phy: ethernet-phy@7 {
		reg = <7>;
		device_type = "ethernet-phy";
	};
};

&gpio0 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_gpio0_default>;
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c0_default>;

	i2c-mux@74 {
		compatible = "nxp,pca9548";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x74>;

		i2c@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			si570: clock-generator@5d {
				#clock-cells = <0>;
				compatible = "silabs,si570";
				temperature-stability = <50>;
				reg = <0x5d>;
				factory-fout = <156250000>;
				clock-frequency = <148500000>;
			};
		};

		i2c@1 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <1>;
			adv7511: hdmi-tx@39 {
				compatible = "adi,adv7511";
				reg = <0x39>;
				adi,input-depth = <8>;
				adi,input-colorspace = "yuv422";
				adi,input-clock = "1x";
				adi,input-style = <3>;
				adi,input-justification = "evenly";
			};
		};

		i2c@2 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <2>;
			eeprom@54 {
				compatible = "atmel,24c08";
				reg = <0x54>;
			};
		};

		i2c@3 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <3>;
			gpio@21 {
				compatible = "ti,tca6416";
				reg = <0x21>;
				gpio-controller;
				#gpio-cells = <2>;
			};
		};

		i2c@4 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <4>;
			rtc@51 {
				compatible = "nxp,pcf8563";
				reg = <0x51>;
			};
		};

		i2c@7 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <7>;
			ucd90120@65 {
				compatible = "ti,ucd90120";
				reg = <0x65>;
			};
		};
	};
};

&pinctrl0 {
	pinctrl_gem0_default: gem0-default {
		mux {
			function = "ethernet0";
			groups = "ethernet0_0_grp";
		};

		conf {
			groups = "ethernet0_0_grp";
			slew-rate = <0>;
			io-standard = <4>;
		};

		conf-rx {
			pins = "MIO22", "MIO23", "MIO24", "MIO25", "MIO26", "MIO27";
			bias-high-impedance;
			low-power-disable;
		};

		conf-tx {
			pins = "MIO16", "MIO17", "MIO18", "MIO19", "MIO20", "MIO21";
			low-power-enable;
			bias-disable;
		};

		mux-mdio {
			function = "mdio0";
			groups = "mdio0_0_grp";
		};

		conf-mdio {
			groups = "mdio0_0_grp";
			slew-rate = <0>;
			io-standard = <1>;
			bias-disable;
		};
	};

	pinctrl_gpio0_default: gpio0-default {
		mux {
			function = "gpio0";
			groups = "gpio0_7_grp", "gpio0_46_grp", "gpio0_47_grp";
		};

		conf {
			groups = "gpio0_7_grp", "gpio0_46_grp", "gpio0_47_grp";
			slew-rate = <0>;
			io-standard = <1>;
		};

		conf-pull-up {
			pins = "MIO46", "MIO47";
			bias-pull-up;
		};

		conf-pull-none {
			pins = "MIO7";
			bias-disable;
		};
	};

	pinctrl_i2c0_default: i2c0-default {
		mux {
			groups = "i2c0_10_grp";
			function = "i2c0";
		};

		conf {
			groups = "i2c0_10_grp";
			bias-pull-up;
			slew-rate = <0>;
			io-standard = <1>;
		};
	};

	pinctrl_sdhci0_default: sdhci0-default {
		mux {
			groups = "sdio0_2_grp";
			function = "sdio0";
		};

		conf {
			groups = "sdio0_2_grp";
			slew-rate = <0>;
			io-standard = <1>;
			bias-disable;
		};

		mux-cd {
			groups = "gpio0_14_grp";
			function = "sdio0_cd";
		};

		conf-cd {
			groups = "gpio0_14_grp";
			bias-high-impedance;
			bias-pull-up;
			slew-rate = <0>;
			io-standard = <1>;
		};

		mux-wp {
			groups = "gpio0_15_grp";
			function = "sdio0_wp";
		};

		conf-wp {
			groups = "gpio0_15_grp";
			bias-high-impedance;
			bias-pull-up;
			slew-rate = <0>;
			io-standard = <1>;
		};
	};

	pinctrl_uart1_default: uart1-default {
		mux {
			groups = "uart1_10_grp";
			function = "uart1";
		};

		conf {
			groups = "uart1_10_grp";
			slew-rate = <0>;
			io-standard = <1>;
		};

		conf-rx {
			pins = "MIO49";
			bias-high-impedance;
		};

		conf-tx {
			pins = "MIO48";
			bias-disable;
		};
	};

	pinctrl_usb0_default: usb0-default {
		mux {
			groups = "usb0_0_grp";
			function = "usb0";
		};

		conf {
			groups = "usb0_0_grp";
			slew-rate = <0>;
			io-standard = <1>;
		};

		conf-rx {
			pins = "MIO29", "MIO31", "MIO36";
			bias-high-impedance;
		};

		conf-tx {
			pins = "MIO28", "MIO30", "MIO32", "MIO33", "MIO34",
			       "MIO35", "MIO37", "MIO38", "MIO39";
			bias-disable;
		};
	};
};

&sdhci0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sdhci0_default>;
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart1_default>;
};

&usb0 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy0>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_usb0_default>;
};
