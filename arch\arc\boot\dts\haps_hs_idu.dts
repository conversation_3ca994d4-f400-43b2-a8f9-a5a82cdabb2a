// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2016-2014 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

/include/ "skeleton_hs_idu.dtsi"

/ {
	model = "snps,zebu_hs-smp";
	compatible = "snps,zebu_hs";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&core_intc>;

	memory {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;	/* 512 */
	};

	chosen {
		bootargs = "earlycon=uart8250,mmio32,0xf0000000,115200n8 console=ttyS0,115200n8 debug print-fatal-signals=1";
	};

	aliases {
		serial0 = &uart0;
	};

	fpga {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* child and parent address space 1:1 mapped */
		ranges;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <50000000>;	/* 50 MHZ */
		};

		core_intc: interrupt-controller {
			compatible = "snps,archs-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		idu_intc: idu-interrupt-controller {
			compatible = "snps,archs-idu-intc";
			interrupt-controller;
			interrupt-parent = <&core_intc>;
			#interrupt-cells = <1>;
		};

		uart0: serial@f0000000 {
			/* compatible = "ns8250"; Doesn't use FIFOs */
			compatible = "ns16550a";
			reg = <0xf0000000 0x2000>;
			interrupt-parent = <&idu_intc>;
			interrupts = <0>;
			clock-frequency = <50000000>;
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
			no-loopback-test = <1>;
		};

		arcpct0: pct {
			compatible = "snps,archs-pct";
			#interrupt-cells = <1>;
			interrupts = <20>;
		};
	};
};
