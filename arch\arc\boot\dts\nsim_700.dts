// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2012 Synopsys, Inc. (www.synopsys.com)
 */
/dts-v1/;

/include/ "skeleton.dtsi"

/ {
	model = "snps,nsim";
	compatible = "snps,nsim";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&core_intc>;

	chosen {
		bootargs = "earlycon=arc_uart,mmio32,0xc0fc1000,115200n8 console=ttyARC0,115200n8 print-fatal-signals=1";
	};

	aliases {
		serial0 = &arcuart0;
	};

	fpga {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;

		/* child and parent address space 1:1 mapped */
		ranges;

		core_clk: core_clk {
			#clock-cells = <0>;
			compatible = "fixed-clock";
			clock-frequency = <80000000>;
		};

		core_intc: interrupt-controller {
			compatible = "snps,arc700-intc";
			interrupt-controller;
			#interrupt-cells = <1>;
		};

		arcuart0: serial@c0fc1000 {
			compatible = "snps,arc-uart";
			reg = <0xc0fc1000 0x100>;
			interrupts = <5>;
			clock-frequency = <80000000>;
			current-speed = <115200>;
			status = "okay";
		};

		ethernet@c0fc2000 {
			compatible = "snps,arc-emac";
			reg = <0xc0fc2000 0x3c>;
			interrupts = <6>;
			mac-address = [ 00 11 22 33 44 55 ];
			clock-frequency = <80000000>;
			max-speed = <100>;
			phy = <&phy0>;

			#address-cells = <1>;
			#size-cells = <0>;
			phy0: ethernet-phy@0 {
				reg = <1>;
			};
		};

		arcpct0: pct {
			compatible = "snps,arc700-pct";
		};
	};
};
