// SPDX-License-Identifier: GPL-2.0+
/*
 * Xilinx CC108 board DTS
 *
 * (C) Copyright 2007-2018 Xilinx, Inc.
 * (C) Copyright 2007-2013 <PERSON><PERSON>
 * (C) Copyright 2007-2012 PetaLogix Qld Pty Ltd
 *
 * <PERSON><PERSON>IM<PERSON>K <<EMAIL>>
 */
/dts-v1/;
/include/ "zynq-7000.dtsi"

/ {
	model = "Xilinx CC108 board";
	compatible = "xlnx,zynq-cc108", "xlnx,zynq-7000";

	aliases {
		ethernet0 = &gem0;
		serial0 = &uart0;
	};

	chosen {
		bootargs = "";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x20000000>;
	};

	usb_phy0: phy0 {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
	};

	usb_phy1: phy1 {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
	};
};

&gem0 {
	status = "okay";
	phy-mode = "rgmii-id";
	phy-handle = <&ethernet_phy>;

	ethernet_phy: ethernet-phy@1 {
		reg = <1>;
		device_type = "ethernet-phy";
	};
};

&sdhci1 {
	status = "okay";
	broken-cd ;
	wp-inverted ;
};

&uart0 {
	status = "okay";
};

&usb0 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy0>;
};

&usb1 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy1>;
};
