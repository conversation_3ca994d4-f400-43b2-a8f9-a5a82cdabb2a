#include "qcom-msm8974.dtsi"
#include "qcom-pm8841.dtsi"
#include "qcom-pm8941.dtsi"
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/pinctrl/qcom,pmic-gpio.h>


/ {
	model = "Fairphone 2";
	compatible = "fairphone,fp2", "qcom,msm8974";

	aliases {
		serial0 = &blsp1_uart2;
	};

	chosen {
		stdout-path = "serial0:115200n8";
	};

	gpio-keys {
		compatible = "gpio-keys";
		input-name = "gpio-keys";

		pinctrl-names = "default";
		pinctrl-0 = <&gpio_keys_pin_a>;

		camera-snapshot {
			label = "camera_snapshot";
			gpios = <&pm8941_gpios 1 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_CAMERA>;
			wakeup-source;
			debounce-interval = <15>;
		};

		volume-down {
			label = "volume_down";
			gpios = <&pm8941_gpios 2 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_VOLUMEDOWN>;
			wakeup-source;
			debounce-interval = <15>;
		};

		volume-up {
			label = "volume_up";
			gpios = <&pm8941_gpios 5 GPIO_ACTIVE_LOW>;
			linux,code = <KEY_VOLUMEUP>;
			wakeup-source;
			debounce-interval = <15>;
		};
	};

	vibrator {
		compatible = "gpio-vibrator";
		enable-gpios = <&msmgpio 86 GPIO_ACTIVE_HIGH>;
		vcc-supply = <&pm8941_l18>;
	};

	smd {
		rpm {
			rpm_requests {
				pm8841-regulators {
					s1 {
						regulator-min-microvolt = <675000>;
						regulator-max-microvolt = <1050000>;
					};

					s2 {
						regulator-min-microvolt = <500000>;
						regulator-max-microvolt = <1050000>;
					};

					s3 {
						regulator-min-microvolt = <1050000>;
						regulator-max-microvolt = <1050000>;
					};
				};

				pm8941-regulators {
					vdd_l1_l3-supply = <&pm8941_s1>;
					vdd_l2_lvs1_2_3-supply = <&pm8941_s3>;
					vdd_l4_l11-supply = <&pm8941_s1>;
					vdd_l5_l7-supply = <&pm8941_s2>;
					vdd_l6_l12_l14_l15-supply = <&pm8941_s2>;
					vdd_l9_l10_l17_l22-supply = <&vreg_boost>;
					vdd_l13_l20_l23_l24-supply = <&vreg_boost>;
					vdd_l21-supply = <&vreg_boost>;

					s1 {
						regulator-min-microvolt = <1300000>;
						regulator-max-microvolt = <1300000>;

						regulator-always-on;
						regulator-boot-on;
					};

					s2 {
						regulator-min-microvolt = <2150000>;
						regulator-max-microvolt = <2150000>;

						regulator-boot-on;
					};

					s3 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;

						regulator-always-on;
						regulator-boot-on;
					};

					l1 {
						regulator-min-microvolt = <1225000>;
						regulator-max-microvolt = <1225000>;

						regulator-always-on;
						regulator-boot-on;
					};

					l2 {
						regulator-min-microvolt = <1200000>;
						regulator-max-microvolt = <1200000>;
					};

					l3 {
						regulator-min-microvolt = <1225000>;
						regulator-max-microvolt = <1225000>;
					};

					l4 {
						regulator-min-microvolt = <1225000>;
						regulator-max-microvolt = <1225000>;
					};

					l5 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;
					};

					l6 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;

						regulator-boot-on;
					};

					l7 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;

						regulator-boot-on;
					};

					l8 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;
					};

					l9 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <2950000>;
					};

					l10 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <2950000>;
					};

					l11 {
						regulator-min-microvolt = <1225000>;
						regulator-max-microvolt = <1350000>;
					};

					l12 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;

						regulator-always-on;
						regulator-boot-on;
					};

					l13 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <2950000>;

						regulator-boot-on;
					};

					l14 {
						regulator-min-microvolt = <1800000>;
						regulator-max-microvolt = <1800000>;
					};

					l15 {
						regulator-min-microvolt = <2050000>;
						regulator-max-microvolt = <2050000>;
					};

					l16 {
						regulator-min-microvolt = <2700000>;
						regulator-max-microvolt = <2700000>;
					};

					l17 {
						regulator-min-microvolt = <2850000>;
						regulator-max-microvolt = <2850000>;
					};

					l18 {
						regulator-min-microvolt = <2850000>;
						regulator-max-microvolt = <2850000>;
					};

					l19 {
						regulator-min-microvolt = <2900000>;
						regulator-max-microvolt = <3350000>;
					};

					l20 {
						regulator-min-microvolt = <2950000>;
						regulator-max-microvolt = <2950000>;

						regulator-boot-on;
					};

					l21 {
						regulator-min-microvolt = <2950000>;
						regulator-max-microvolt = <2950000>;

						regulator-boot-on;
					};

					l22 {
						regulator-min-microvolt = <3000000>;
						regulator-max-microvolt = <3300000>;
					};

					l23 {
						regulator-min-microvolt = <3000000>;
						regulator-max-microvolt = <3000000>;
					};

					l24 {
						regulator-min-microvolt = <3075000>;
						regulator-max-microvolt = <3075000>;

						regulator-boot-on;
					};
				};
			};
		};
	};
};

&soc {
	serial@f991e000 {
		status = "ok";
	};

	pinctrl@fd510000 {
		sdhc1_pin_a: sdhc1-pin-active {
			clk {
				pins = "sdc1_clk";
				drive-strength = <16>;
				bias-disable;
			};

			cmd-data {
				pins = "sdc1_cmd", "sdc1_data";
				drive-strength = <10>;
				bias-pull-up;
			};
		};

		sdhc2_cd_pin_a: sdhc2-cd-pin-active {
			pins = "gpio62";
			function = "gpio";

			drive-strength = <2>;
			bias-disable;
		};

		sdhc2_pin_a: sdhc2-pin-active {
			clk {
				pins = "sdc2_clk";
				drive-strength = <10>;
				bias-disable;
			};

			cmd-data {
				pins = "sdc2_cmd", "sdc2_data";
				drive-strength = <6>;
				bias-pull-up;
			};
		};
	};

	sdhci@f9824900 {
		status = "ok";

		vmmc-supply = <&pm8941_l20>;
		vqmmc-supply = <&pm8941_s3>;

		bus-width = <8>;
		non-removable;

		pinctrl-names = "default";
		pinctrl-0 = <&sdhc1_pin_a>;
	};

	sdhci@f98a4900 {
		status = "ok";

		vmmc-supply = <&pm8941_l21>;
		vqmmc-supply = <&pm8941_l13>;

		bus-width = <4>;

		pinctrl-names = "default";
		pinctrl-0 = <&sdhc2_pin_a>, <&sdhc2_cd_pin_a>;
	};

	usb@f9a55000 {
		status = "ok";

		phys = <&usb_hs1_phy>;
		phy-select = <&tcsr 0xb000 0>;
		extcon = <&smbb>, <&usb_id>;
		vbus-supply = <&chg_otg>;

		hnp-disable;
		srp-disable;
		adp-disable;

		ulpi {
			phy@a {
				status = "ok";

				v1p8-supply = <&pm8941_l6>;
				v3p3-supply = <&pm8941_l24>;

				extcon = <&smbb>;
				qcom,init-seq = /bits/ 8 <0x1 0x64>;
			};
		};
	};
};

&spmi_bus {
	pm8941@0 {
		gpios@c000 {
			gpio_keys_pin_a: gpio-keys-active {
				pins = "gpio1", "gpio2", "gpio5";
				function = "normal";

				bias-pull-up;
				power-source = <PM8941_GPIO_S3>;
			};
		};
	};
};
