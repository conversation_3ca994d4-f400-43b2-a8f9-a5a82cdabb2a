# SPDX-License-Identifier: GPL-2.0
#
# For a description of the syntax of this configuration file,
# see Documentation/kbuild/kconfig-language.rst.
#

menuconfig ARC_PLAT_EZNPS
	bool "\"EZchip\" ARC dev platform"
	select CPU_BIG_ENDIAN
	select CLKSRC_NPS
	select EZNPS_GIC
	select EZCHIP_NPS_MANAGEMENT_ENET if ETHERNET
	help
	  Support for EZchip development platforms,
	  based on ARC700 cores.
	  We handle few flavors:
	    - Hardware Emulator AKA HE which is FPGA based chassis
	    - Simulator based on MetaWare nSIM
	    - NPS400 chip based on ASIC

config EZNPS_MTM_EXT
	bool "ARC-EZchip MTM Extensions"
	select CPUMASK_OFFSTACK
	depends on ARC_PLAT_EZNPS && SMP
	default y
	help
	  Here we add new hierarchy for CPUs topology.
	  We got:
	    Core
	    Thread
	  At the new thread level each CPU represent one HW thread.
	  At highest hierarchy each core contain 16 threads,
	  any of them seem like CPU from Linux point of view.
	  All threads within same core share the execution unit of the
	  core and HW scheduler round robin between them.

config EZNPS_MEM_ERROR_ALIGN
	bool "ARC-EZchip Memory error as an exception"
	depends on EZNPS_MTM_EXT
	default n
	help
	  On the real chip of the NPS, user memory errors are handled
	  as a machine check exception, which is fatal, whereas on
	  simulator platform for NPS, is handled as a Level 2 interrupt
	  (just a stock ARC700) which is recoverable. This option makes
	  simulator behave like hardware.

config EZNPS_SHARED_AUX_REGS
	bool "ARC-EZchip Shared Auxiliary Registers Per Core"
	depends on ARC_PLAT_EZNPS
	default y
	help
	  On the real chip of the NPS, auxiliary registers are shared between
	  all the cpus of the core, whereas on simulator platform for NPS,
	  each cpu has a different set of auxiliary registers. Configuration
	  should be unset if auxiliary registers are not shared between the cpus
	  of the core, so there will be a need to initialize them per cpu.
