// SPDX-License-Identifier: GPL-2.0
/dts-v1/;

#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/clock/qcom,gcc-msm8960.h>
#include <dt-bindings/mfd/qcom-rpm.h>
#include <dt-bindings/soc/qcom,gsbi.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	model = "Qualcomm MSM8960";
	compatible = "qcom,msm8960";
	interrupt-parent = <&intc>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		interrupts = <1 14 0x304>;

		cpu@0 {
			compatible = "qcom,krait";
			enable-method = "qcom,kpss-acc-v1";
			device_type = "cpu";
			reg = <0>;
			next-level-cache = <&L2>;
			qcom,acc = <&acc0>;
			qcom,saw = <&saw0>;
		};

		cpu@1 {
			compatible = "qcom,krait";
			enable-method = "qcom,kpss-acc-v1";
			device_type = "cpu";
			reg = <1>;
			next-level-cache = <&L2>;
			qcom,acc = <&acc1>;
			qcom,saw = <&saw1>;
		};

		L2: l2-cache {
			compatible = "cache";
			cache-level = <2>;
		};
	};

	memory {
		device_type = "memory";
		reg = <0x0 0x0>;
	};

	cpu-pmu {
		compatible = "qcom,krait-pmu";
		interrupts = <1 10 0x304>;
		qcom,no-pc-write;
	};

	clocks {
		cxo_board {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <19200000>;
			clock-output-names = "cxo_board";
		};

		pxo_board {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <27000000>;
			clock-output-names = "pxo_board";
		};

		sleep_clk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <32768>;
			clock-output-names = "sleep_clk";
		};
	};

	soc: soc {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		compatible = "simple-bus";

		intc: interrupt-controller@2000000 {
			compatible = "qcom,msm-qgic2";
			interrupt-controller;
			#interrupt-cells = <3>;
			reg = <0x02000000 0x1000>,
			      <0x02002000 0x1000>;
		};

		timer@200a000 {
			compatible = "qcom,kpss-timer",
				     "qcom,kpss-wdt-msm8960", "qcom,msm-timer";
			interrupts = <1 1 0x301>,
				     <1 2 0x301>,
				     <1 3 0x301>;
			reg = <0x0200a000 0x100>;
			clock-frequency = <27000000>,
					  <32768>;
			cpu-offset = <0x80000>;
		};

		msmgpio: pinctrl@800000 {
			compatible = "qcom,msm8960-pinctrl";
			gpio-controller;
			#gpio-cells = <2>;
			interrupts = <0 16 0x4>;
			interrupt-controller;
			#interrupt-cells = <2>;
			reg = <0x800000 0x4000>;
		};

		gcc: clock-controller@900000 {
			compatible = "qcom,gcc-msm8960";
			#clock-cells = <1>;
			#reset-cells = <1>;
			reg = <0x900000 0x4000>;
		};

		lcc: clock-controller@28000000 {
			compatible = "qcom,lcc-msm8960";
			reg = <0x28000000 0x1000>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		clock-controller@4000000 {
			compatible = "qcom,mmcc-msm8960";
			reg = <0x4000000 0x1000>;
			#clock-cells = <1>;
			#reset-cells = <1>;
		};

		l2cc: clock-controller@2011000 {
			compatible	= "syscon";
			reg		= <0x2011000 0x1000>;
		};

		rpm@108000 {
			compatible	= "qcom,rpm-msm8960";
			reg		= <0x108000 0x1000>;
			qcom,ipc	= <&l2cc 0x8 2>;

			interrupts	= <0 19 0>, <0 21 0>, <0 22 0>;
			interrupt-names	= "ack", "err", "wakeup";

			regulators {
				compatible = "qcom,rpm-pm8921-regulators";
			};
		};

		acc0: clock-controller@2088000 {
			compatible = "qcom,kpss-acc-v1";
			reg = <0x02088000 0x1000>, <0x02008000 0x1000>;
		};

		acc1: clock-controller@2098000 {
			compatible = "qcom,kpss-acc-v1";
			reg = <0x02098000 0x1000>, <0x02008000 0x1000>;
		};

		saw0: regulator@2089000 {
			compatible = "qcom,saw2";
			reg = <0x02089000 0x1000>, <0x02009000 0x1000>;
			regulator;
		};

		saw1: regulator@2099000 {
			compatible = "qcom,saw2";
			reg = <0x02099000 0x1000>, <0x02009000 0x1000>;
			regulator;
		};

		gsbi5: gsbi@16400000 {
			compatible = "qcom,gsbi-v1.0.0";
			cell-index = <5>;
			reg = <0x16400000 0x100>;
			clocks = <&gcc GSBI5_H_CLK>;
			clock-names = "iface";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			syscon-tcsr = <&tcsr>;

			gsbi5_serial: serial@16440000 {
				compatible = "qcom,msm-uartdm-v1.3", "qcom,msm-uartdm";
				reg = <0x16440000 0x1000>,
				      <0x16400000 0x1000>;
				interrupts = <0 154 0x0>;
				clocks = <&gcc GSBI5_UART_CLK>, <&gcc GSBI5_H_CLK>;
				clock-names = "core", "iface";
				status = "disabled";
			};
		};

		qcom,ssbi@500000 {
			compatible = "qcom,ssbi";
			reg = <0x500000 0x1000>;
			qcom,controller-type = "pmic-arbiter";

			pmicintc: pmic@0 {
				compatible = "qcom,pm8921";
				interrupt-parent = <&msmgpio>;
				interrupts = <104 8>;
				#interrupt-cells = <2>;
				interrupt-controller;
				#address-cells = <1>;
				#size-cells = <0>;

				pwrkey@1c {
					compatible = "qcom,pm8921-pwrkey";
					reg = <0x1c>;
					interrupt-parent = <&pmicintc>;
					interrupts = <50 1>, <51 1>;
					debounce = <15625>;
					pull-up;
				};

				keypad@148 {
					compatible = "qcom,pm8921-keypad";
					reg = <0x148>;
					interrupt-parent = <&pmicintc>;
					interrupts = <74 1>, <75 1>;
					debounce = <15>;
					scan-delay = <32>;
					row-hold = <91500>;
				};

				rtc@11d {
					compatible = "qcom,pm8921-rtc";
					interrupt-parent = <&pmicintc>;
					interrupts = <39 1>;
					reg = <0x11d>;
					allow-set-time;
				};
			};
		};

		rng@1a500000 {
			compatible = "qcom,prng";
			reg = <0x1a500000 0x200>;
			clocks = <&gcc PRNG_CLK>;
			clock-names = "core";
		};

		/* Temporary fixed regulator */
		vsdcc_fixed: vsdcc-regulator {
			compatible = "regulator-fixed";
			regulator-name = "SDCC Power";
			regulator-min-microvolt = <2700000>;
			regulator-max-microvolt = <2700000>;
			regulator-always-on;
		};

		amba {
			compatible = "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;
			sdcc1: sdcc@12400000 {
				status		= "disabled";
				compatible	= "arm,pl18x", "arm,primecell";
				arm,primecell-periphid = <0x00051180>;
				reg		= <0x12400000 0x8000>;
				interrupts	= <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names	= "cmd_irq";
				clocks		= <&gcc SDC1_CLK>, <&gcc SDC1_H_CLK>;
				clock-names	= "mclk", "apb_pclk";
				bus-width	= <8>;
				max-frequency	= <96000000>;
				non-removable;
				cap-sd-highspeed;
				cap-mmc-highspeed;
				vmmc-supply = <&vsdcc_fixed>;
			};

			sdcc3: sdcc@12180000 {
				compatible	= "arm,pl18x", "arm,primecell";
				arm,primecell-periphid = <0x00051180>;
				status		= "disabled";
				reg		= <0x12180000 0x8000>;
				interrupts	= <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names	= "cmd_irq";
				clocks		= <&gcc SDC3_CLK>, <&gcc SDC3_H_CLK>;
				clock-names	= "mclk", "apb_pclk";
				bus-width	= <4>;
				cap-sd-highspeed;
				cap-mmc-highspeed;
				max-frequency	= <192000000>;
				no-1-8-v;
				vmmc-supply = <&vsdcc_fixed>;
			};
		};

		tcsr: syscon@1a400000 {
			compatible = "qcom,tcsr-msm8960", "syscon";
			reg = <0x1a400000 0x100>;
		};

		gsbi@16000000 {
			compatible = "qcom,gsbi-v1.0.0";
			cell-index = <1>;
			reg = <0x16000000 0x100>;
			clocks = <&gcc GSBI1_H_CLK>;
			clock-names = "iface";
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			spi@16080000 {
				compatible = "qcom,spi-qup-v1.1.1";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x16080000 0x1000>;
				interrupts = <0 147 0>;
				spi-max-frequency = <24000000>;
				cs-gpios = <&msmgpio 8 0>;

				clocks = <&gcc GSBI1_QUP_CLK>, <&gcc GSBI1_H_CLK>;
				clock-names = "core", "iface";
				status = "disabled";
			};
		};
	};
};
