/*
 * Copyright 2013-2017 <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License
 *     version 2 as published by the Free Software Foundation.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 */

/dts-v1/;
#include "imx53-tx53.dtsi"
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "Ka-Ro electronics TX53 module (LCD)";
	compatible = "karo,tx53", "fsl,imx53";

	aliases {
		display = &display;
	};

	display: disp0 {
		compatible = "fsl,imx-parallel-display";
		interface-pix-fmt = "rgb24";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_rgb24_vga1>;
		status = "okay";

		port {
			display0_in: endpoint {
				remote-endpoint = <&ipu_di0_disp0>;
			};
		};

		display-timings {
			VGA {
				clock-frequency = <25200000>;
				hactive = <640>;
				vactive = <480>;
				hback-porch = <48>;
				hsync-len = <96>;
				hfront-porch = <16>;
				vback-porch = <31>;
				vsync-len = <2>;
				vfront-porch = <12>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <0>;
			};

			ETV570 {
				clock-frequency = <25200000>;
				hactive = <640>;
				vactive = <480>;
				hback-porch = <114>;
				hsync-len = <30>;
				hfront-porch = <16>;
				vback-porch = <32>;
				vsync-len = <3>;
				vfront-porch = <10>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <0>;
			};

			ET0350 {
				clock-frequency = <6413760>;
				hactive = <320>;
				vactive = <240>;
				hback-porch = <34>;
				hsync-len = <34>;
				hfront-porch = <20>;
				vback-porch = <15>;
				vsync-len = <3>;
				vfront-porch = <4>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <0>;
			};

			ET0430 {
				clock-frequency = <9009000>;
				hactive = <480>;
				vactive = <272>;
				hback-porch = <2>;
				hsync-len = <41>;
				hfront-porch = <2>;
				vback-porch = <2>;
				vsync-len = <10>;
				vfront-porch = <2>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <1>;
			};

			ET0500 {
				clock-frequency = <33264000>;
				hactive = <800>;
				vactive = <480>;
				hback-porch = <88>;
				hsync-len = <128>;
				hfront-porch = <40>;
				vback-porch = <33>;
				vsync-len = <2>;
				vfront-porch = <10>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <0>;
			};

			ET0700 { /* same as ET0500 */
				clock-frequency = <33264000>;
				hactive = <800>;
				vactive = <480>;
				hback-porch = <88>;
				hsync-len = <128>;
				hfront-porch = <40>;
				vback-porch = <33>;
				vsync-len = <2>;
				vfront-porch = <10>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <0>;
			};

			ETQ570 {
				clock-frequency = <6596040>;
				hactive = <320>;
				vactive = <240>;
				hback-porch = <38>;
				hsync-len = <30>;
				hfront-porch = <30>;
				vback-porch = <16>;
				vsync-len = <3>;
				vfront-porch = <4>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <0>;
			};
		};
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm2 0 500000 PWM_POLARITY_INVERTED>;
		power-supply = <&reg_3v3>;
		brightness-levels = <
			  0  1  2  3  4  5  6  7  8  9
			 10 11 12 13 14 15 16 17 18 19
			 20 21 22 23 24 25 26 27 28 29
			 30 31 32 33 34 35 36 37 38 39
			 40 41 42 43 44 45 46 47 48 49
			 50 51 52 53 54 55 56 57 58 59
			 60 61 62 63 64 65 66 67 68 69
			 70 71 72 73 74 75 76 77 78 79
			 80 81 82 83 84 85 86 87 88 89
			 90 91 92 93 94 95 96 97 98 99
			100
		>;
		default-brightness-level = <50>;
	};

	reg_lcd_pwr: regulator-lcd-pwr {
		compatible = "regulator-fixed";
		regulator-name = "LCD POWER";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio2 31 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};

	reg_lcd_reset: regulator-lcd-reset {
		compatible = "regulator-fixed";
		regulator-name = "LCD RESET";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio3 29 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};
};

&i2c3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c3>;
	status = "okay";

	sgtl5000: codec@a {
		compatible = "fsl,sgtl5000";
		reg = <0x0a>;
		#sound-dai-cells = <0>;
		VDDA-supply = <&reg_2v5>;
		VDDIO-supply = <&reg_3v3>;
		clocks = <&mclk>;
	};

	polytouch: edt-ft5x06@38 {
		compatible = "edt,edt-ft5x06";
		reg = <0x38>;
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_edt_ft5x06_1>;
		interrupt-parent = <&gpio6>;
		interrupts = <15 IRQ_TYPE_EDGE_FALLING>;
		reset-gpios = <&gpio2 22 GPIO_ACTIVE_LOW>;
		wake-gpios = <&gpio2 21 GPIO_ACTIVE_HIGH>;
		wakeup-source;
	};

	touchscreen: tsc2007@48 {
		compatible = "ti,tsc2007";
		reg = <0x48>;
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_tsc2007>;
		interrupt-parent = <&gpio3>;
		interrupts = <26 IRQ_TYPE_EDGE_FALLING>;
		gpios = <&gpio3 26 GPIO_ACTIVE_LOW>;
		ti,x-plate-ohms = <660>;
		wakeup-source;
	};
};

&iomuxc {
	imx53-tx53-x03x {
		pinctrl_edt_ft5x06_1: edt-ft5x06grp-1 {
			fsl,pins = <
				MX53_PAD_NANDF_CS2__GPIO6_15 0x1f0 /* Interrupt */
				MX53_PAD_EIM_A16__GPIO2_22   0x04 /* Reset */
				MX53_PAD_EIM_A17__GPIO2_21   0x04 /* Wake */
			>;
		};

		pinctrl_kpp: kppgrp {
			fsl,pins = <
				MX53_PAD_GPIO_9__KPP_COL_6 0x1f4
				MX53_PAD_GPIO_4__KPP_COL_7 0x1f4
				MX53_PAD_KEY_COL2__KPP_COL_2 0x1f4
				MX53_PAD_KEY_COL3__KPP_COL_3 0x1f4
				MX53_PAD_GPIO_2__KPP_ROW_6 0x1f4
				MX53_PAD_GPIO_5__KPP_ROW_7 0x1f4
				MX53_PAD_KEY_ROW2__KPP_ROW_2 0x1f4
				MX53_PAD_KEY_ROW3__KPP_ROW_3 0x1f4
			>;
		};

		pinctrl_rgb24_vga1: rgb24-vgagrp1 {
			fsl,pins = <
				MX53_PAD_DI0_DISP_CLK__IPU_DI0_DISP_CLK		0x5
				MX53_PAD_DI0_PIN15__IPU_DI0_PIN15		0x5
				MX53_PAD_DI0_PIN2__IPU_DI0_PIN2			0x5
				MX53_PAD_DI0_PIN3__IPU_DI0_PIN3			0x5
				MX53_PAD_DISP0_DAT0__IPU_DISP0_DAT_0		0x5
				MX53_PAD_DISP0_DAT1__IPU_DISP0_DAT_1		0x5
				MX53_PAD_DISP0_DAT2__IPU_DISP0_DAT_2		0x5
				MX53_PAD_DISP0_DAT3__IPU_DISP0_DAT_3		0x5
				MX53_PAD_DISP0_DAT4__IPU_DISP0_DAT_4		0x5
				MX53_PAD_DISP0_DAT5__IPU_DISP0_DAT_5		0x5
				MX53_PAD_DISP0_DAT6__IPU_DISP0_DAT_6		0x5
				MX53_PAD_DISP0_DAT7__IPU_DISP0_DAT_7		0x5
				MX53_PAD_DISP0_DAT8__IPU_DISP0_DAT_8		0x5
				MX53_PAD_DISP0_DAT9__IPU_DISP0_DAT_9		0x5
				MX53_PAD_DISP0_DAT10__IPU_DISP0_DAT_10		0x5
				MX53_PAD_DISP0_DAT11__IPU_DISP0_DAT_11		0x5
				MX53_PAD_DISP0_DAT12__IPU_DISP0_DAT_12		0x5
				MX53_PAD_DISP0_DAT13__IPU_DISP0_DAT_13		0x5
				MX53_PAD_DISP0_DAT14__IPU_DISP0_DAT_14		0x5
				MX53_PAD_DISP0_DAT15__IPU_DISP0_DAT_15		0x5
				MX53_PAD_DISP0_DAT16__IPU_DISP0_DAT_16		0x5
				MX53_PAD_DISP0_DAT17__IPU_DISP0_DAT_17		0x5
				MX53_PAD_DISP0_DAT18__IPU_DISP0_DAT_18		0x5
				MX53_PAD_DISP0_DAT19__IPU_DISP0_DAT_19		0x5
				MX53_PAD_DISP0_DAT20__IPU_DISP0_DAT_20		0x5
				MX53_PAD_DISP0_DAT21__IPU_DISP0_DAT_21		0x5
				MX53_PAD_DISP0_DAT22__IPU_DISP0_DAT_22		0x5
				MX53_PAD_DISP0_DAT23__IPU_DISP0_DAT_23		0x5
			>;
		};

		pinctrl_tsc2007: tsc2007grp {
			fsl,pins = <
				MX53_PAD_EIM_D26__GPIO3_26 0x1f0 /* Interrupt */
			>;
		};
	};
};

&ipu_di0_disp0 {
	remote-endpoint = <&display0_in>;
};

&kpp {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_kpp>;
	/* sample keymap */
	/* row/col 0,1 are mapped to KPP row/col 6,7 */
	linux,keymap = <
		MATRIX_KEY(6, 6, KEY_POWER)
		MATRIX_KEY(6, 7, KEY_KP0)
		MATRIX_KEY(6, 2, KEY_KP1)
		MATRIX_KEY(6, 3, KEY_KP2)
		MATRIX_KEY(7, 6, KEY_KP3)
		MATRIX_KEY(7, 7, KEY_KP4)
		MATRIX_KEY(7, 2, KEY_KP5)
		MATRIX_KEY(7, 3, KEY_KP6)
		MATRIX_KEY(2, 6, KEY_KP7)
		MATRIX_KEY(2, 7, KEY_KP8)
		MATRIX_KEY(2, 2, KEY_KP9)
	>;
	status = "okay";
};
