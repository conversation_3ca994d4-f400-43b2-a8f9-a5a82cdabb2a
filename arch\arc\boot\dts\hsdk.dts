// SPDX-License-Identifier: GPL-2.0-only
/*
 * Copyright (C) 2017 Synopsys, Inc. (www.synopsys.com)
 */

/*
 * Device Tree for ARC HS Development Kit
 */
/dts-v1/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/reset/snps,hsdk-reset.h>

/ {
	model = "snps,hsdk";
	compatible = "snps,hsdk";

	#address-cells = <2>;
	#size-cells = <2>;

	chosen {
		bootargs = "earlycon=uart8250,mmio32,0xf0005000,115200n8 console=ttyS0,115200n8 debug print-fatal-signals=1";
	};

	aliases {
		ethernet = &gmac;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <0>;
			clocks = <&core_clk>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <1>;
			clocks = <&core_clk>;
		};

		cpu@2 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <2>;
			clocks = <&core_clk>;
		};

		cpu@3 {
			device_type = "cpu";
			compatible = "snps,archs38";
			reg = <3>;
			clocks = <&core_clk>;
		};
	};

	input_clk: input-clk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <33333333>;
	};

	reg_5v0: regulator-5v0 {
		compatible = "regulator-fixed";

		regulator-name = "5v0-supply";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	cpu_intc: cpu-interrupt-controller {
		compatible = "snps,archs-intc";
		interrupt-controller;
		#interrupt-cells = <1>;
	};

	idu_intc: idu-interrupt-controller {
		compatible = "snps,archs-idu-intc";
		interrupt-controller;
		#interrupt-cells = <1>;
		interrupt-parent = <&cpu_intc>;
	};

	arcpct: pct {
		compatible = "snps,archs-pct";
	};

	/* TIMER0 with interrupt for clockevent */
	timer {
		compatible = "snps,arc-timer";
		interrupts = <16>;
		interrupt-parent = <&cpu_intc>;
		clocks = <&core_clk>;
	};

	/* 64-bit Global Free Running Counter */
	gfrc {
		compatible = "snps,archs-timer-gfrc";
		clocks = <&core_clk>;
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		interrupt-parent = <&idu_intc>;

		ranges = <0x00000000 0x0 0xf0000000 0x10000000>;

		cgu_rst: reset-controller@8a0 {
			compatible = "snps,hsdk-reset";
			#reset-cells = <1>;
			reg = <0x8a0 0x4>, <0xff0 0x4>;
		};

		core_clk: core-clk@0 {
			compatible = "snps,hsdk-core-pll-clock";
			reg = <0x00 0x10>, <0x14b8 0x4>;
			#clock-cells = <0>;
			clocks = <&input_clk>;

			/*
			 * Set initial core pll output frequency to 1GHz.
			 * It will be applied at the core pll driver probing
			 * on early boot.
			 */
			assigned-clocks = <&core_clk>;
			assigned-clock-rates = <1000000000>;
		};

		serial: serial@5000 {
			compatible = "snps,dw-apb-uart";
			reg = <0x5000 0x100>;
			clock-frequency = <33330000>;
			interrupts = <6>;
			baud = <115200>;
			reg-shift = <2>;
			reg-io-width = <4>;
		};

		gmacclk: gmacclk {
			compatible = "fixed-clock";
			clock-frequency = <400000000>;
			#clock-cells = <0>;
		};

		mmcclk_ciu: mmcclk-ciu {
			compatible = "fixed-clock";
			/*
			 * DW sdio controller has external ciu clock divider
			 * controlled via register in SDIO IP. Due to its
			 * unexpected default value (it should divide by 1
			 * but it divides by 8) SDIO IP uses wrong clock and
			 * works unstable (see STAR 9001204800)
			 * We switched to the minimum possible value of the
			 * divisor (div-by-2) in HSDK platform code.
			 * So add temporary fix and change clock frequency
			 * to 50000000 Hz until we fix dw sdio driver itself.
			 */
			clock-frequency = <50000000>;
			#clock-cells = <0>;
		};

		mmcclk_biu: mmcclk-biu {
			compatible = "fixed-clock";
			clock-frequency = <400000000>;
			#clock-cells = <0>;
		};

		gpu_core_clk: gpu-core-clk {
			compatible = "fixed-clock";
			clock-frequency = <400000000>;
			#clock-cells = <0>;
		};

		gpu_dma_clk: gpu-dma-clk {
			compatible = "fixed-clock";
			clock-frequency = <400000000>;
			#clock-cells = <0>;
		};

		gpu_cfg_clk: gpu-cfg-clk {
			compatible = "fixed-clock";
			clock-frequency = <200000000>;
			#clock-cells = <0>;
		};

		dmac_core_clk: dmac-core-clk {
			compatible = "fixed-clock";
			clock-frequency = <400000000>;
			#clock-cells = <0>;
		};

		dmac_cfg_clk: dmac-gpu-cfg-clk {
			compatible = "fixed-clock";
			clock-frequency = <200000000>;
			#clock-cells = <0>;
		};

		gmac: ethernet@8000 {
			#interrupt-cells = <1>;
			compatible = "snps,dwmac";
			reg = <0x8000 0x2000>;
			interrupts = <10>;
			interrupt-names = "macirq";
			phy-mode = "rgmii";
			snps,pbl = <32>;
			snps,multicast-filter-bins = <256>;
			clocks = <&gmacclk>;
			clock-names = "stmmaceth";
			phy-handle = <&phy0>;
			resets = <&cgu_rst HSDK_ETH_RESET>;
			reset-names = "stmmaceth";
			mac-address = [00 00 00 00 00 00]; /* Filled in by U-Boot */
			dma-coherent;

			tx-fifo-depth = <4096>;
			rx-fifo-depth = <4096>;

			mdio {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "snps,dwmac-mdio";
				phy0: ethernet-phy@0 {
					reg = <0>;
				};
			};
		};

		ohci@60000 {
			compatible = "snps,hsdk-v1.0-ohci", "generic-ohci";
			reg = <0x60000 0x100>;
			interrupts = <15>;
			resets = <&cgu_rst HSDK_USB_RESET>;
			dma-coherent;
		};

		ehci@40000 {
			compatible = "snps,hsdk-v1.0-ehci", "generic-ehci";
			reg = <0x40000 0x100>;
			interrupts = <15>;
			resets = <&cgu_rst HSDK_USB_RESET>;
			dma-coherent;
		};

		mmc@a000 {
			compatible = "altr,socfpga-dw-mshc";
			reg = <0xa000 0x400>;
			num-slots = <1>;
			fifo-depth = <16>;
			card-detect-delay = <200>;
			clocks = <&mmcclk_biu>, <&mmcclk_ciu>;
			clock-names = "biu", "ciu";
			interrupts = <12>;
			bus-width = <4>;
			dma-coherent;
		};

		spi0: spi@20000 {
			compatible = "snps,dw-apb-ssi";
			reg = <0x20000 0x100>;
			#address-cells = <1>;
			#size-cells = <0>;
			interrupts = <16>;
			num-cs = <2>;
			reg-io-width = <4>;
			clocks = <&input_clk>;
			cs-gpios = <&creg_gpio 0 GPIO_ACTIVE_LOW>,
				   <&creg_gpio 1 GPIO_ACTIVE_LOW>;

			spi-flash@0 {
				compatible = "sst26wf016b", "jedec,spi-nor";
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <1>;
				spi-max-frequency = <4000000>;
			};

			adc@1 {
				compatible = "ti,adc108s102";
				reg = <1>;
				vref-supply = <&reg_5v0>;
				spi-max-frequency = <1000000>;
			};
		};

		creg_gpio: gpio@14b0 {
			compatible = "snps,creg-gpio-hsdk";
			reg = <0x14b0 0x4>;
			gpio-controller;
			#gpio-cells = <2>;
			ngpios = <2>;
		};

		gpio: gpio@3000 {
			compatible = "snps,dw-apb-gpio";
			reg = <0x3000 0x20>;
			#address-cells = <1>;
			#size-cells = <0>;

			gpio_port_a: gpio-controller@0 {
				compatible = "snps,dw-apb-gpio-port";
				gpio-controller;
				#gpio-cells = <2>;
				snps,nr-gpios = <24>;
				reg = <0>;
			};
		};

		gpu_3d: gpu@90000 {
			compatible = "vivante,gc";
			reg = <0x90000 0x4000>;
			clocks = <&gpu_dma_clk>,
				 <&gpu_cfg_clk>,
				 <&gpu_core_clk>,
				 <&gpu_core_clk>;
			clock-names = "bus", "reg", "core", "shader";
			interrupts = <28>;
		};

		dmac: dmac@80000 {
			compatible = "snps,axi-dma-1.01a";
			reg = <0x80000 0x400>;
			interrupts = <27>;
			clocks = <&dmac_core_clk>, <&dmac_cfg_clk>;
			clock-names = "core-clk", "cfgr-clk";

			dma-channels = <4>;
			snps,dma-masters = <2>;
			snps,data-width = <3>;
			snps,block-size = <4096 4096 4096 4096>;
			snps,priority = <0 1 2 3>;
			snps,axi-max-burst-len = <16>;
		};
	};

	memory@80000000 {
		#address-cells = <2>;
		#size-cells = <2>;
		device_type = "memory";
		reg = <0x0 0x80000000 0x0 0x40000000>;  /* 1 GB lowmem */
		/*     0x1 0x00000000 0x0 0x40000000>;     1 GB highmem */
	};
};
