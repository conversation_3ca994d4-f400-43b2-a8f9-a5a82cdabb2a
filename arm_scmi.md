# ARM SCMI 驅動程式使用說明文件

## 概述

ARM SCMI (System Control and Management Interface) 是一個標準化的介面協議，用於應用處理器 (AP) 與系統控制處理器 (SCP) 之間的通信。它提供了電源管理、時鐘控制、性能調節、感測器監控等功能。

### 架構圖

```
應用處理器 AP
    ↓
SCMI 驅動程式
    ↓
Mailbox 傳輸層
    ↓
共享記憶體 SRAM
    ↓
系統控制處理器 SCP
```

### 支援的協議

- **Performance Protocol (0x13)**: CPU 頻率和電壓調節 (DVFS)
- **Clock Protocol (0x14)**: 系統時鐘管理
- **Power Domain Protocol (0x11)**: 電源域控制
- **Sensor Protocol (0x15)**: 溫度和其他感測器監控
- **Reset Protocol (0x16)**: 設備重置控制
- **Base Protocol (0x10)**: 基本協議管理

## 核心組件

### 主要資料結構

#### 訊息標頭
```c
struct scmi_msg_hdr {
    u8 id;              // 訊息識別碼
    u8 protocol_id;     // 協議識別碼
    u16 seq;            // 序列號
    u32 status;         // 傳輸狀態
    bool poll_completion; // 輪詢完成標誌
};
```

#### 訊息傳輸結構
```c
struct scmi_xfer {
    struct scmi_msg_hdr hdr;    // 傳輸訊息標頭
    struct scmi_msg tx;         // 傳輸訊息
    struct scmi_msg rx;         // 接收訊息
    struct completion done;     // 完成事件
    struct completion *async_done; // 異步完成事件
};
```

### 核心 API 函數

```c
// 取得 SCMI 控制代碼
struct scmi_handle *scmi_handle_get(struct device *dev);

// 初始化訊息傳輸
int scmi_xfer_get_init(const struct scmi_handle *h, u8 msg_id, u8 prot_id,
                       size_t tx_size, size_t rx_size, struct scmi_xfer **p);

// 執行訊息傳輸
int scmi_do_xfer(const struct scmi_handle *h, struct scmi_xfer *xfer);

// 釋放訊息傳輸資源
void scmi_xfer_put(const struct scmi_handle *h, struct scmi_xfer *xfer);
```

## Device Tree 配置

### 基本配置範例

```dts
sram@50000000 {
    compatible = "mmio-sram";
    reg = <0x0 0x50000000 0x0 0x10000>;
    #address-cells = <1>;
    #size-cells = <1>;
    ranges = <0 0x0 0x50000000 0x10000>;

    cpu_scp_lpri: scp-shmem@0 {
        compatible = "arm,scmi-shmem";
        reg = <0x0 0x200>;
    };

    cpu_scp_hpri: scp-shmem@200 {
        compatible = "arm,scmi-shmem";
        reg = <0x200 0x200>;
    };
};

mailbox@40000000 {
    #mbox-cells = <1>;
    reg = <0x0 0x40000000 0x0 0x10000>;
};

firmware {
    scmi {
        compatible = "arm,scmi";
        mboxes = <&mailbox 0 &mailbox 1>;
        mbox-names = "tx", "rx";
        shmem = <&cpu_scp_lpri &cpu_scp_hpri>;
        #address-cells = <1>;
        #size-cells = <0>;

        scmi_devpd: protocol@11 {
            reg = <0x11>;
            #power-domain-cells = <1>;
        };

        scmi_dvfs: protocol@13 {
            reg = <0x13>;
            #clock-cells = <1>;
        };

        scmi_clk: protocol@14 {
            reg = <0x14>;
            #clock-cells = <1>;
        };

        scmi_sensors0: protocol@15 {
            reg = <0x15>;
            #thermal-sensor-cells = <1>;
        };

        scmi_reset: protocol@16 {
            reg = <0x16>;
            #reset-cells = <1>;
        };
    };
};
```

### CPU 節點配置

```dts
cpu@0 {
    compatible = "arm,cortex-a53";
    reg = <0 0>;
    clocks = <&scmi_dvfs 0>;
    power-domains = <&scmi_devpd 0>;
    #cooling-cells = <2>;
};
```

## 驅動程式使用方法

### 1. CPUFreq SCMI 驅動程式

#### 初始化流程
```c
static int scmi_cpufreq_init(struct cpufreq_policy *policy)
{
    int ret, nr_opp;
    struct device *cpu_dev;
    struct scmi_data *priv;
    struct cpufreq_frequency_table *freq_table;

    // 取得 CPU 設備
    cpu_dev = get_cpu_device(policy->cpu);
    if (!cpu_dev) {
        pr_err("failed to get cpu%d device\n", policy->cpu);
        return -ENODEV;
    }

    // 添加 OPP 到設備
    ret = handle->perf_ops->device_opps_add(handle, cpu_dev);
    if (ret) {
        dev_warn(cpu_dev, "failed to add opps to the device\n");
        return ret;
    }

    // 設定共享 CPU
    ret = scmi_get_sharing_cpus(cpu_dev, policy->cpus);
    
    // 初始化頻率表
    ret = dev_pm_opp_init_cpufreq_table(cpu_dev, &freq_table);
    
    policy->freq_table = freq_table;
    policy->dvfs_possible_from_any_cpu = true;
    policy->fast_switch_possible = true;

    return 0;
}
```

#### 頻率設定
```c
static int scmi_cpufreq_set_target(struct cpufreq_policy *policy, 
                                   unsigned int index)
{
    int ret;
    struct scmi_data *priv = policy->driver_data;
    struct scmi_perf_ops *perf_ops = handle->perf_ops;
    u64 freq = policy->freq_table[index].frequency;

    ret = perf_ops->freq_set(handle, priv->domain_id, freq * 1000, false);
    if (!ret)
        arch_set_freq_scale(policy->related_cpus, freq,
                            policy->cpuinfo.max_freq);
    return ret;
}
```

#### 快速頻率切換
```c
static unsigned int scmi_cpufreq_fast_switch(struct cpufreq_policy *policy,
                                             unsigned int target_freq)
{
    struct scmi_data *priv = policy->driver_data;
    struct scmi_perf_ops *perf_ops = handle->perf_ops;

    if (!perf_ops->freq_set(handle, priv->domain_id,
                            target_freq * 1000, true)) {
        arch_set_freq_scale(policy->related_cpus, target_freq,
                            policy->cpuinfo.max_freq);
        return target_freq;
    }

    return 0;
}
```

### 2. Reset 控制器

#### Reset 操作實現
```c
static int scmi_reset_assert(struct reset_controller_dev *rcdev, 
                             unsigned long id)
{
    const struct scmi_handle *handle = to_scmi_handle(rcdev);
    return handle->reset_ops->assert(handle, id);
}

static int scmi_reset_deassert(struct reset_controller_dev *rcdev, 
                               unsigned long id)
{
    const struct scmi_handle *handle = to_scmi_handle(rcdev);
    return handle->reset_ops->deassert(handle, id);
}

static int scmi_reset_reset(struct reset_controller_dev *rcdev, 
                            unsigned long id)
{
    const struct scmi_handle *handle = to_scmi_handle(rcdev);
    return handle->reset_ops->reset(handle, id);
}
```

#### Reset 控制器註冊
```c
static const struct reset_control_ops scmi_reset_ops = {
    .assert     = scmi_reset_assert,
    .deassert   = scmi_reset_deassert,
    .reset      = scmi_reset_reset,
};
```

### 3. 時鐘管理

#### 時鐘操作
```c
// 啟用時鐘
static int scmi_clk_enable(struct clk_hw *hw)
{
    struct scmi_clk *clk = to_scmi_clk(hw);
    return clk->handle->clk_ops->enable(clk->handle, clk->id);
}

// 停用時鐘
static void scmi_clk_disable(struct clk_hw *hw)
{
    struct scmi_clk *clk = to_scmi_clk(hw);
    clk->handle->clk_ops->disable(clk->handle, clk->id);
}

// 設定時鐘頻率
static int scmi_clk_set_rate(struct clk_hw *hw, unsigned long rate,
                             unsigned long parent_rate)
{
    struct scmi_clk *clk = to_scmi_clk(hw);
    return clk->handle->clk_ops->rate_set(clk->handle, clk->id, rate);
}
```

## 使用步驟

### 步驟 1: 內核配置

在內核配置中啟用以下選項：

```bash
CONFIG_ARM_SCMI_PROTOCOL=y
CONFIG_ARM_SCMI_CPUFREQ=y
CONFIG_RESET_SCMI=y
CONFIG_CLK_SCMI=y
CONFIG_ARM_SCMI_POWER_DOMAIN=y
CONFIG_MAILBOX=y
CONFIG_ARM_MHU=y
```

### 步驟 2: Device Tree 設定

1. 配置共享記憶體區域 (SRAM)
2. 設定 mailbox 通道
3. 定義 SCMI 協議節點
4. 將設備連接到 SCMI 服務

### 步驟 3: 驅動程式註冊

```c
// SCMI 設備驅動程式註冊
static struct scmi_driver scmi_cpufreq_drv = {
    .name       = "scmi-cpufreq",
    .probe      = scmi_cpufreq_probe,
    .remove     = scmi_cpufreq_remove,
    .id_table   = scmi_id_table,
};
module_scmi_driver(scmi_cpufreq_drv);
```

### 步驟 4: 運行時使用

#### CPUFreq 控制
```bash
# 查看可用頻率
cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_available_frequencies

# 設定 governor
echo "performance" > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor

# 查看當前頻率
cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq

# 設定最大頻率
echo 1800000 > /sys/devices/system/cpu/cpu0/cpufreq/scaling_max_freq
```

#### Reset 控制
```c
// 在驅動程式中使用 reset
struct reset_control *rst;

rst = devm_reset_control_get(&pdev->dev, "scmi-reset");
if (IS_ERR(rst))
    return PTR_ERR(rst);

// Assert reset
reset_control_assert(rst);

// Deassert reset
reset_control_deassert(rst);

// 執行完整重置週期
reset_control_reset(rst);
```

#### 時鐘控制
```c
// 取得時鐘
struct clk *clk = devm_clk_get(&pdev->dev, "scmi-clk");

// 設定時鐘頻率
clk_set_rate(clk, 100000000); // 100MHz

// 啟用時鐘
clk_prepare_enable(clk);

// 停用時鐘
clk_disable_unprepare(clk);
```

## 協議支援詳細

| 協議 ID | 名稱 | 功能 | 驅動程式檔案 |
|---------|------|------|-------------|
| 0x10 | Base | 基本協議管理、版本查詢 | base.c |
| 0x11 | Power Domain | 電源域開關控制 | scmi_pm_domain.c |
| 0x13 | Performance | 性能/DVFS 控制、OPP 管理 | perf.c |
| 0x14 | Clock | 時鐘頻率設定、啟用/停用 | clock.c |
| 0x15 | Sensor | 溫度、電壓等感測器讀取 | sensors.c |
| 0x16 | Reset | 設備重置控制 | reset.c |

## 除錯與監控

### 除錯介面

```bash
# 查看 SCMI 狀態
cat /sys/kernel/debug/scmi/

# 啟用 SCMI 追蹤
echo 1 > /sys/kernel/debug/tracing/events/scmi/enable

# 查看追蹤記錄
cat /sys/kernel/debug/tracing/trace

# 監控特定協議
echo 1 > /sys/kernel/debug/tracing/events/scmi/scmi_xfer_begin/enable
echo 1 > /sys/kernel/debug/tracing/events/scmi/scmi_xfer_end/enable
```

### 常見問題排除

#### 1. 通信失敗
- **症狀**: SCMI 訊息傳輸超時
- **檢查項目**:
  - Mailbox 配置是否正確
  - 共享記憶體地址是否正確
  - SCP 韌體是否正常運行

#### 2. 協議不支援
- **症狀**: 特定協議功能無法使用
- **檢查項目**:
  - SCP 韌體版本是否支援該協議
  - Device Tree 中協議節點是否正確配置
  - 驅動程式是否已載入

#### 3. 性能問題
- **症狀**: 頻率切換延遲過高
- **解決方案**:
  - 啟用 fast_switch 功能
  - 調整訊息超時設定
  - 優化共享記憶體大小

### 錯誤碼對照

| 錯誤碼 | 名稱 | 描述 |
|--------|------|------|
| 0 | SCMI_SUCCESS | 成功 |
| -1 | SCMI_ERR_SUPPORT | 不支援 |
| -2 | SCMI_ERR_PARAMS | 參數錯誤 |
| -3 | SCMI_ERR_ACCESS | 存取被拒 |
| -4 | SCMI_ERR_ENTRY | 找不到項目 |
| -5 | SCMI_ERR_RANGE | 數值超出範圍 |
| -6 | SCMI_ERR_BUSY | 設備忙碌 |
| -7 | SCMI_ERR_COMMS | 通信錯誤 |
| -8 | SCMI_ERR_GENERIC | 一般錯誤 |
| -9 | SCMI_ERR_HARDWARE | 硬體錯誤 |
| -10 | SCMI_ERR_PROTOCOL | 協議錯誤 |

## 性能優化建議

### 1. 使用 Fast Switch
```c
// 在 CPUFreq 驅動程式中啟用
policy->fast_switch_possible = true;
```

### 2. 批次操作
- 合併多個相關操作
- 減少 mailbox 通信開銷
- 使用異步訊息傳輸

### 3. 記憶體優化
- 適當調整共享記憶體大小
- 使用專用通道處理高優先級訊息

### 4. 監控與調優
```bash
# 監控頻率切換延遲
cat /sys/kernel/debug/tracing/trace | grep scmi

# 調整 governor 參數
echo 50000 > /sys/devices/system/cpu/cpufreq/ondemand/sampling_rate
```

## 開發指南

### 新增自定義協議

1. **定義協議結構**
```c
struct scmi_custom_ops {
    int (*custom_func)(const struct scmi_handle *handle, u32 param);
};
```

2. **實現協議處理函數**
```c
static int scmi_custom_protocol_init(struct scmi_handle *handle)
{
    // 協議初始化邏輯
    return 0;
}
```

3. **註冊協議**
```c
static const struct scmi_protocol scmi_custom = {
    .id = SCMI_PROTOCOL_CUSTOM,
    .init = &scmi_custom_protocol_init,
    .ops = &scmi_custom_ops,
};
```

### 最佳實踐

1. **錯誤處理**: 總是檢查 SCMI 操作的返回值
2. **資源管理**: 正確釋放 xfer 資源
3. **同步處理**: 適當使用 completion 機制
4. **效能考量**: 避免在中斷上下文中進行 SCMI 操作

## 參考資料

- [ARM SCMI 規範文件](http://infocenter.arm.com/help/topic/com.arm.doc.den0056a/index.html)
- [Linux 內核 SCMI 文檔](Documentation/devicetree/bindings/arm/arm,scmi.txt)
- [CPUFreq 子系統文檔](Documentation/cpu-freq/)
- [Reset 框架文檔](Documentation/devicetree/bindings/reset/reset.txt)

---

**版本**: 1.0  
**更新日期**: 2024年  
**作者**: Linux 內核開發團隊
