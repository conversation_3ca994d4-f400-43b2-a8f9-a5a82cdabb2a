// SPDX-License-Identifier: GPL-2.0+
/*
 * Xilinx ZC770 XM012 board DTS
 *
 * Copyright (C) 2013-2018 Xilinx, Inc.
 */
/dts-v1/;
#include "zynq-7000.dtsi"

/ {
	model = "Xilinx ZC770 XM012 board";
	compatible = "xlnx,zynq-zc770-xm012", "xlnx,zynq-7000";

	aliases {
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		serial0 = &uart1;
		spi0 = &spi1;
	};

	chosen {
		bootargs = "";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};
};

&can1 {
	status = "okay";
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;

	eeprom0: eeprom@52 {
		compatible = "atmel,24c02";
		reg = <0x52>;
	};
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	eeprom1: eeprom@52 {
		compatible = "atmel,24c02";
		reg = <0x52>;
	};
};

&spi1 {
	status = "okay";
	num-cs = <4>;
	is-decoded-cs = <0>;
};

&uart1 {
	status = "okay";
};
