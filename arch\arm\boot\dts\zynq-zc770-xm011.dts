// SPDX-License-Identifier: GPL-2.0+
/*
 * Xilinx ZC770 XM013 board DTS
 *
 * Copyright (C) 2013-2018 Xilinx, Inc.
 */
/dts-v1/;
#include "zynq-7000.dtsi"

/ {
	model = "Xilinx ZC770 XM011 board";
	compatible = "xlnx,zynq-zc770-xm011", "xlnx,zynq-7000";

	aliases {
		i2c0 = &i2c1;
		serial0 = &uart1;
		spi0 = &spi0;
	};

	chosen {
		bootargs = "";
		stdout-path = "serial0:115200n8";
	};

	memory@0 {
		device_type = "memory";
		reg = <0x0 0x40000000>;
	};

	usb_phy1: phy1 {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
	};
};

&can0 {
	status = "okay";
};

&i2c1 {
	status = "okay";
	clock-frequency = <400000>;

	eeprom: eeprom@52 {
		compatible = "atmel,24c02";
		reg = <0x52>;
	};
};

&spi0 {
	status = "okay";
	num-cs = <4>;
	is-decoded-cs = <0>;
};

&uart1 {
	status = "okay";
};

&usb1 {
	status = "okay";
	dr_mode = "host";
	usb-phy = <&usb_phy1>;
};
