// SPDX-License-Identifier: GPL-2.0

#include <dt-bindings/clock/zx296702-clock.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		enable-method = "zte,zx296702-smp";

		cpu@0 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2cc>;
			reg = <0>;
		};

		cpu@1 {
			compatible = "arm,cortex-a9";
			device_type = "cpu";
			next-level-cache = <&l2cc>;
			reg = <1>;
		};
	};


	soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		interrupt-parent = <&intc>;
		ranges;

		matrix: bus-matrix@400000 {
			compatible = "zte,zx-bus-matrix";
			reg = <0x00400000 0x1000>;
		};

		intc: interrupt-controller@801000 {
			compatible = "arm,cortex-a9-gic";
			#interrupt-cells = <3>;
			#address-cells = <1>;
			#size-cells = <1>;
			interrupt-controller;
			reg = <0x00801000 0x1000>,
			      <0x00800100 0x100>;
		};

		global_timer: timer@8000200 {
			compatible = "arm,cortex-a9-global-timer";
			reg = <0x00800200 0x20>;
			interrupts = <GIC_PPI 11 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-parent = <&intc>;
			clocks = <&topclk ZX296702_A9_PERIPHCLK>;
		};

		l2cc: l2-cache-controller@c00000 {
			compatible = "arm,pl310-cache";
			reg = <0x00c00000 0x1000>;
			cache-unified;
			cache-level = <2>;
			arm,data-latency = <1 1 1>;
			arm,tag-latency = <1 1 1>;
			arm,double-linefill = <1>;
			arm,double-linefill-incr = <0>;
		};

		pcu: pcu@a0008000 {
			compatible = "zte,zx296702-pcu";
			reg = <0xa0008000 0x1000>;
		};

		topclk: topclk@9800000 {
			compatible = "zte,zx296702-topcrm-clk";
			reg = <0x09800000 0x1000>;
			#clock-cells = <1>;
		};

		lsp1clk: lsp1clk@9400000 {
			compatible = "zte,zx296702-lsp1crpm-clk";
			reg = <0x09400000 0x1000>;
			#clock-cells = <1>;
		};

		lsp0clk: lsp0clk@b000000 {
			compatible = "zte,zx296702-lsp0crpm-clk";
			reg = <0x0b000000 0x1000>;
			#clock-cells = <1>;
		};

		uart0: serial@9405000 {
			compatible = "zte,zx296702-uart";
			reg = <0x09405000 0x1000>;
			interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&lsp1clk ZX296702_UART0_WCLK>;
			status = "disabled";
		};

		uart1: serial@9406000 {
			compatible = "zte,zx296702-uart";
			reg = <0x09406000 0x1000>;
			interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&lsp1clk ZX296702_UART1_WCLK>;
			status = "disabled";
		};

		mmc0: mmc@9408000 {
			compatible = "snps,dw-mshc";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x09408000 0x1000>;
			interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
			fifo-depth = <32>;
			clocks = <&lsp1clk ZX296702_SDMMC0_PCLK>,
				 <&lsp1clk ZX296702_SDMMC0_WCLK>;
			clock-names = "biu", "ciu";
			status = "disabled";
		};

		mmc1: mmc@b003000 {
			compatible = "snps,dw-mshc";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0b003000 0x1000>;
			interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
			fifo-depth = <32>;
			clocks = <&lsp0clk ZX296702_SDMMC1_PCLK>,
				 <&lsp0clk ZX296702_SDMMC1_WCLK>;
			clock-names = "biu", "ciu";
			status = "disabled";
		};

		sysctrl: sysctrl@a0007000 {
			compatible = "zte,sysctrl", "syscon";
			reg = <0xa0007000 0x1000>;
		};
	};
};
