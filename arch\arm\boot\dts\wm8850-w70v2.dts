// SPDX-License-Identifier: GPL-2.0-or-later
/*
 * wm8850-w70v2.dts
 *  - Device tree file for Wondermedia WM8850 Tablet
 *  - 'W70-V2' mainboard
 *  - HongLianYing 'HLY070ML268-21A' 7" LCD panel
 *
 * Copyright (C) 2012 <PERSON> <<EMAIL>>
 */

/dts-v1/;
/include/ "wm8850.dtsi"
#include <dt-bindings/pwm/pwm.h>

/ {
	model = "Wondermedia WM8850-W70v2 Tablet";

	backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm 0 50000 PWM_POLARITY_INVERTED>;

		brightness-levels = <0 40 60 80 100 130 190 255>;
		default-brightness-level = <5>;
	};
};

&fb {
	bits-per-pixel = <16>;
	display-timings {
		native-mode = <&timing0>;
		timing0: 800x480 {
			clock-frequency = <0>; /* unused but required */
			hactive = <800>;
			vactive = <480>;
			hfront-porch = <40>;
			hback-porch = <88>;
			hsync-len = <0>;
			vback-porch = <32>;
			vfront-porch = <11>;
			vsync-len = <1>;
		};
	};
};

&uart0 {
	status = "okay";
};
