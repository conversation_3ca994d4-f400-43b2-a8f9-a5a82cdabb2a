/*
 * Copyright 2013-2017 <PERSON><PERSON> <<EMAIL>>
 *
 * This file is dual-licensed: you can use it either under the terms
 * of the GPL or the X11 license, at your option. Note that this dual
 * licensing only applies to this file, and not this project as a
 * whole.
 *
 *  a) This file is free software; you can redistribute it and/or
 *     modify it under the terms of the GNU General Public License
 *     version 2 as published by the Free Software Foundation.
 *
 *     This file is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 * Or, alternatively,
 *
 *  b) Permission is hereby granted, free of charge, to any person
 *     obtaining a copy of this software and associated documentation
 *     files (the "Software"), to deal in the Software without
 *     restriction, including without limitation the rights to use,
 *     copy, modify, merge, publish, distribute, sublicense, and/or
 *     sell copies of the Software, and to permit persons to whom the
 *     Software is furnished to do so, subject to the following
 *     conditions:
 *
 *     The above copyright notice and this permission notice shall be
 *     included in all copies or substantial portions of the Software.
 *
 *     THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *     EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *     OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *     NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *     HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *     WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *     FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *     OTHER DEALINGS IN THE SOFTWARE.
 * The code contained herein is licensed under the GNU General Public
 * License. You may obtain a copy of the GNU General Public License
 * Version 2 at the following locations:
 *
 * http://www.opensource.org/licenses/gpl-license.html
 * http://www.gnu.org/copyleft/gpl.html
 */

/dts-v1/;
#include "imx53-tx53.dtsi"
#include <dt-bindings/input/input.h>

/ {
	model = "Ka-Ro electronics TX53 module (LVDS)";
	compatible = "karo,tx53", "fsl,imx53";

	aliases {
		display = &lvds0;
		lvds0 = &lvds0;
		lvds1 = &lvds1;
	};

	backlight0: backlight0 {
		compatible = "pwm-backlight";
		pwms = <&pwm2 0 500000 0>;
		power-supply = <&reg_3v3>;
		brightness-levels = <
			  0  1  2  3  4  5  6  7  8  9
			 10 11 12 13 14 15 16 17 18 19
			 20 21 22 23 24 25 26 27 28 29
			 30 31 32 33 34 35 36 37 38 39
			 40 41 42 43 44 45 46 47 48 49
			 50 51 52 53 54 55 56 57 58 59
			 60 61 62 63 64 65 66 67 68 69
			 70 71 72 73 74 75 76 77 78 79
			 80 81 82 83 84 85 86 87 88 89
			 90 91 92 93 94 95 96 97 98 99
			100
		>;
		default-brightness-level = <50>;
	};

	backlight1: backlight1 {
		compatible = "pwm-backlight";
		pwms = <&pwm1 0 500000 0>;
		power-supply = <&reg_3v3>;
		brightness-levels = <
			  0  1  2  3  4  5  6  7  8  9
			 10 11 12 13 14 15 16 17 18 19
			 20 21 22 23 24 25 26 27 28 29
			 30 31 32 33 34 35 36 37 38 39
			 40 41 42 43 44 45 46 47 48 49
			 50 51 52 53 54 55 56 57 58 59
			 60 61 62 63 64 65 66 67 68 69
			 70 71 72 73 74 75 76 77 78 79
			 80 81 82 83 84 85 86 87 88 89
			 90 91 92 93 94 95 96 97 98 99
			100
		>;
		default-brightness-level = <50>;
	};

	reg_lcd_pwr0: regulator-lvds0-pwr {
		compatible = "regulator-fixed";
		regulator-name = "LVDS0 POWER";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio3 29 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};

	reg_lcd_pwr1: regulator-lvds1-pwr {
		compatible = "regulator-fixed";
		regulator-name = "LVDS1 POWER";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio2 31 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};
};

&i2c3 {
	pinctrl-names = "default", "gpio";
	pinctrl-0 = <&pinctrl_i2c3>;
	pinctrl-1 = <&pinctrl_i2c3_gpio>;
	scl-gpios = <&gpio3 21 GPIO_ACTIVE_HIGH>;
	sda-gpios = <&gpio3 28 GPIO_ACTIVE_HIGH>;
	status = "okay";

	sgtl5000: codec@a {
		compatible = "fsl,sgtl5000";
		reg = <0x0a>;
		#sound-dai-cells = <0>;
		VDDA-supply = <&reg_2v5>;
		VDDIO-supply = <&reg_3v3>;
		clocks = <&mclk>;
	};
};

&iomuxc {
	imx53-tx53-x13x {
		pinctrl_lvds0: lvds0grp {
			fsl,pins = <
				MX53_PAD_LVDS0_TX3_P__LDB_LVDS0_TX3 0x80000000
				MX53_PAD_LVDS0_CLK_P__LDB_LVDS0_CLK 0x80000000
				MX53_PAD_LVDS0_TX2_P__LDB_LVDS0_TX2 0x80000000
				MX53_PAD_LVDS0_TX1_P__LDB_LVDS0_TX1 0x80000000
				MX53_PAD_LVDS0_TX0_P__LDB_LVDS0_TX0 0x80000000
			>;
		};

		pinctrl_lvds1: lvds1grp {
			fsl,pins = <
				MX53_PAD_LVDS1_TX3_P__LDB_LVDS1_TX3 0x80000000
				MX53_PAD_LVDS1_TX2_P__LDB_LVDS1_TX2 0x80000000
				MX53_PAD_LVDS1_CLK_P__LDB_LVDS1_CLK 0x80000000
				MX53_PAD_LVDS1_TX1_P__LDB_LVDS1_TX1 0x80000000
				MX53_PAD_LVDS1_TX0_P__LDB_LVDS1_TX0 0x80000000
			>;
		};

		pinctrl_pwm1: pwm1grp {
			fsl,pins = <MX53_PAD_GPIO_9__PWM1_PWMO 0x04>;
		};

		pinctrl_eeti1: eeti1grp {
			fsl,pins = <
				MX53_PAD_EIM_D22__GPIO3_22 0x1f0 /* Interrupt */
			>;
		};

		pinctrl_eeti2: eeti2grp {
			fsl,pins = <
				MX53_PAD_EIM_D23__GPIO3_23 0x1f0 /* Interrupt */
			>;
		};
	};
};

&ldb {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lvds0 &pinctrl_lvds1>;
	status = "okay";

	lvds0: lvds-channel@0 {
		fsl,data-mapping = "spwg";
		fsl,data-width = <18>;
		status = "okay";

		display-timings {
			native-mode = <&lvds0_timing0>;

			lvds0_timing0: hsd100pxn1 {
				clock-frequency = <65000000>;
				hactive = <1024>;
				vactive = <768>;
				hback-porch = <220>;
				hsync-len = <60>;
				hfront-porch = <40>;
				vback-porch = <21>;
				vsync-len = <10>;
				vfront-porch = <7>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <1>;
			};

			lvds0_timing1: nl12880bc20 {
				clock-frequency = <71000000>;
				hactive = <1280>;
				vactive = <800>;
				hback-porch = <50>;
				hsync-len = <60>;
				hfront-porch = <50>;
				vback-porch = <5>;
				vsync-len = <13>;
				vfront-porch = <5>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <1>;
			};
		};
	};

	lvds1: lvds-channel@1 {
		fsl,data-mapping = "spwg";
		fsl,data-width = <18>;
		status = "okay";

		display-timings {
			native-mode = <&lvds1_timing0>;

			lvds1_timing0: hsd100pxn1 {
				clock-frequency = <65000000>;
				hactive = <1024>;
				vactive = <768>;
				hback-porch = <220>;
				hsync-len = <60>;
				hfront-porch = <40>;
				vback-porch = <21>;
				vsync-len = <10>;
				vfront-porch = <7>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <1>;
				pixelclk-active = <1>;
			};
		};
	};
};

&pwm1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm1>;
};

&sata {
	status = "okay";
};
