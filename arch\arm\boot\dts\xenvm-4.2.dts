// SPDX-License-Identifier: GPL-2.0
/*
 * Xen Virtual Machine for unprivileged guests
 *
 * Based on ARM Ltd. Versatile Express CoreTile Express (single CPU)
 * Cortex-A15 MPCore (V2P-CA15)
 *
 */

/dts-v1/;

/ {
	model = "XENVM-4.2";
	compatible = "xen,xenvm-4.2", "xen,xenvm";
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	chosen {
		/* this field is going to be adjusted by the hypervisor */
		bootargs = "console=hvc0 root=/dev/xvda";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <0>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a15";
			reg = <1>;
		};
	};

	psci {
		compatible      = "arm,psci";
		method          = "hvc";
		cpu_off         = <1>;
		cpu_on          = <2>;
	};

	memory@80000000 {
		device_type = "memory";
		/* this field is going to be adjusted by the hypervisor */
		reg = <0 0x80000000 0 0x08000000>;
	};

	gic: interrupt-controller@2c001000 {
		compatible = "arm,cortex-a15-gic", "arm,cortex-a9-gic";
		#interrupt-cells = <3>;
		#address-cells = <0>;
		interrupt-controller;
		reg = <0 0x2c001000 0 0x1000>,
		      <0 0x2c002000 0 0x100>;
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <1 13 0xf08>,
			     <1 14 0xf08>,
			     <1 11 0xf08>,
			     <1 10 0xf08>;
	};

	hypervisor {
		compatible = "xen,xen-4.2", "xen,xen";
		/* this field is going to be adjusted by the hypervisor */
		reg = <0 0xb0000000 0 0x20000>;
		/* this field is going to be adjusted by the hypervisor */
		interrupts = <1 15 0xf08>;
	};

	motherboard {
		arm,v2m-memory-map = "rs1";
	};
};
